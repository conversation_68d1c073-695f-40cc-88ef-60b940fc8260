<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8">
		<title>Endless Runner</title>
		<meta property="og:title" content="Endless Runner"/>
		<meta property="og:type" content="website"/>
		<meta property="og:site_name" content="Endless Runner"/>
		<meta name="theme-color" content="#000000"/>
		<meta name="mobile-web-app-capable" content="yes"/>
		<meta name="apple-mobile-web-app-capable" content="yes"/>
		<link rel="manifest" href="EndlessRunner.json"/>
		<link rel="icon" sizes="16x16" type="image/png" href="EndlessRunnerFavIcon_16x16.png"/>
		<link rel="icon" sizes="192x192" type="image/png" href="EndlessRunnerFavIcon_192x192.png"/>
		<link rel="apple-touch-icon" href="data:image/png;base64,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"/>
		<link rel="apple-touch-startup-image" href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMAAAADACAYAAABS3GwHAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH5AoDEwkE0csmAgAAIABJREFUeNrsvXecXFd99/8+55bpW7VardpqVSzZlpssFwyu2JTQCTY1wEMaIbQQAvklD79fQgIJ5AkhPaTQEppTSOgdYxtjY4pxkWzLkmX17dNnbjnn/P44d2burOQCAcPm2ePXatezZe7M/ZbPt32+whhjWDkr5//SI1fegpWzogArZ+WsKMDKWTkrCrByVs6KAqyclbOiACtn5awowMpZOSsKsHJWzooCrJyVs6IAK2flrCjAylk5KwqwclbOigKsnJWzogArZ+Us4+Mux4t+pA5uIQTGGIQQJ/1s53udr7XWCCFOerzz+w/3PI/0PQBtDMaAAVwpSS7lpBNGMdXAEMaaUGnCMKbRCphrKqqNNmEYU8hn8R3BSMGnkHHIZDx818F3JTnfYSDrdF9r+sTKYDBIAfJhLmDp63i01/Vo7494uBf6M3zEcpoH+Fm61FMqmRCIUwhCI1BMLzY4Xmkz31DM1kMemm1wZL7FwdmAxUbIYhDTaMUErZBmpFD25oAGgSHnSjzPxfcEpZzHcMFnfCDD1tU5tk0U2LCqwKqCw+T4AOtG8v0KqTvK/WMUnBUF+NlXgLSQnsrK/3du9iPd9Llqm7uONTk41+LeIwvcPxtycKbKA0crtLVEITBagzKgNRgDUoDGug1jQGC/r0w/UDXaSrIQyQ8DjoMjNDlHc9bUCKdPFDl3coDTN45w8dZhivnsf/u9fKzeYUUBHicFOJX7Tv/Mw92wR3L7p3LvDwellp47D1e58b55vneoyr1HKuybbrDQVJg4xiBASCvsSiMFVgEwGCM6uCl5kYkCGNP/GMnjHVMuBMLp3ESBJlEiDAiFEJLBrODcyQHOnRxi99YRnrZrPaMD+R/pPX00aLgchX/ZKsCjWaOl8OS/e3M6zyVlf87g8HyDj3zzMF/YW+HAiSpHy22M1hhtQFnL7ggBiESmjZVlnQi4ECmBx1p9rROBt7/X+1qCicEo+3sy/ZrsLwtpYZOQSRyirfdwjMZxDNvXFrl42zAvetIkV5wz2RcbaG0eESIZY7qv/9FisBUFeByOUgqDwHVkX/AZxyoxkDa47WDyHybgS1u6WIMrBTIRuNlKixvuW+RfvnWUWx9YZK4WWIzdEWKtTw1fOvAG07PoUvQgkMUyIBz7faWA5EMp+7ibtUqA7iXwRMpLyES5tOk9Tuq50AgUDpqdGwd45ZWTvPSK7awaKiRBucJz5WP2nqfyoCse4HE4WpuuQOooIo5jfN8HxzlZUbRGIB7RuqVvXOft0MYgE8UB2Huszn985xh/f8NDHF4MMUr1LHnaEJuOEGp6Zr0j+CIFY2yACxKka616HEDUhLAKQR3iBqgWBDWIW7DqTBg5w34tZT8k6jxH17ukIROp6+hoo0LokNPGs7z1unN42VU78Fw30WODeAThfzg4tKIAjyMEqtUbXP+l2/nOfYeIjaaUyzExNsyakUE2jBbYNDHKpo3jpCPI9Et9OGjUebzzvT3H6vzrt4/yt187yGw1QsfqZGHuWP8+3L4E0/cJorFCjwsmsgIeVaFdhqBlBVR4IHz7syqExjEwIWy8CnKjoIOeUKf1TKnec3RgFkuuQSTXbhSYCEnINeeM8bYX7eKJZ216TJBoBQL9lM//81f/wd9+6S6awscIFwk4KKSO8UzEaWuHOXvjKLu3TnDNxaezeXLdI8YTaYwLsO9Ejb+/6SjXf+swhxfavaxNX0CaEq6TvEEa1ycxgQAcHxwXVBta89BagDhMIE4e3AK4OZCONdQ6tp6hOQPV/TC8GdbsgrBhlVAkGpDEGv2Qqi+C7n3Z0QSRuCwVI0xAzgn5jeedxe+9/Am4nv9DxV0rHuBxzL1/b88BnvF713MizJDJFa1cGuu2TRyh4hCI8VQbXwVsHPK58qxJXnT1Li69cGcfHEjDKYCFesCff/lBPnTzYQ7NtzFK43RkXCcpSJ2kIxH9wqZNP843pgdLhGMF30RQPwHtihVgLw/ZAcgUQXoQadDWggsijFagYvvzC3sgW4J1TwQV9Qt3N2DuwCtzsuVPB9wyrcgaB4OKQxwRcskZw/zd66/kjKmJR/QGKxDop4T9//LfbubNH7mFUJaQfhbdyQcaEDpx60YjdUQcNDFhE081KUnNRVtW8avPv4xnXHEBru/35df/8esHePcXHuKB6QZGa5wkA6N0Ki3ZydywNE2ZBL0diKFNTzA71xfMQ2MatITMIGQHwc9aKBQZaLWgXYe4bpWjOIDI5ayMBnWY3wNCwfpLEwim+p/fGBsbpNOqndpBn0Mw9LsDm3mSErSKQbfZuMrhr19/Jc98wnYAYqVwpFzxAD8L581//Wn+4sv3EbmDCD+LkU4iaMLebBVZqxmHEAcIHWKCFkQtpGqTI2DX5nHe+KIr+LkrLuD2wyFv+didfPvAIlpbqy5MIjfa9GCMWWpJU7BH6wSSiF42RghwPNAtqE9D2LaCnxlE+h5IF9000Kox0D7GWnmcc1dV2LWqyX3zkg8f20Y0fBpkMtBuwOIDEDdh7UVWaXTcHwAvFfJOnNJVgJSH6nqFfmUQEhvgmwCPJn/xmst49fMu7DNAP0xR8Gf9LJteoG7WIY44NLuIMoCUGCGthe1YJ5XADant3cTBaAekD55NBDYiwU17HuL23/8gF900zw/Kg5Sr9ZRQC0waxuhTBLVp9NGJB0QnLSns9QgBwSK0Fm1AW1oPfhbhSHQDqFbYIA9w5fB+nnJGmfPXC8bHBxgaW01Qr6Ouf4gPHptAjOfs65RO4l00OEuC2z6406kh6CVv4lIvsMQ7YDAqKdrhE8WK1/75V9Am5jXPvwQpbf+UlPJR+6VWFODHrQCJwaq32lSbEUZ0MhqyZwU1PVyusVjaJB8dKCJc0AoKa2iv2sE3DmdB1VOYfgm+10uqskuvqCsAolu76rqJdgWiFmRGwcvZ61Rg5tpkG/u4du1eXrFzkQs258kOTxDnBhG+RyubI79xLZftOMAHH6pgzDhCSJucVFG/Mp50SaaHcMypfoiTA/UOfEp7BkC4GVRseMNffJXRgSwvvHoXUkobCz1M1mxFAX7CGhDHCqVVrx9G0J/z7gir0laQu48LiLVNE45MwvAWkFkIm/0ZEpPCzSYlTGIJrhZLhEuanqJpBXHbRpr5MXAcCy2aBqan2V34Pm++5ChXn5GhOLqeIDtE2/dwXBfj+iiRgVyRsTWDZGWbNtIWsjo9Q53X21HUpSn+zkWbpbAn9TqFTnkv3VOeVArVAEif2BR5/Z99gbM3jXH61g1dD5AW/uVaDFt28wBS2JJ/13X3wREFcQyx6jWaddKXcWQFc2gTjO6wLz1sJEIgesWrpYrUh/VT3kCZVKCbEjyTKJ7jg1+ysAWBaWuKjf381mk38m/Pnea5T1yNGFtPIzuEyGSR2TwiX0IWBpHZIvhFsvkcnmOVSnSCa+nZa1f6ZFimTwHNThWvoFOP6Z7X63q7JZDKzTLT8HjVOz9JvVE/qY6SVoZlJ0/LJlpP3lvfc8m4rs326ESodWyD3jCCKIYosgFwFFmFiJrW4o1OwcC6REniXpDYERyNFSyleq0LnUBY018D0EsVIwU5pGuDX5IAGgnViJdsOsS7X2HYsHUjNX8Ev1QkM1DCLZVw84PI7ADSz9mWByeDcXxMcotMJ9DGA5xeT1C/S+pZ+nSwLtJeTfUMg1HJR6e9Ivm603OUvGghATfHbftrvO49n7KCsyQjtFzjgWXnAXK5HMP5DIJOticR9jC0Vl6F9iOO7E2O2zZOGNkK+dWJ0sQ966dPkTNPew+j+y2oMUvgRNKoJjuQTKZSpSmvkMtwS3Uz3zi8AblmiFVrRtB+CZktITMFpJ9FeD5IFyFdkIIggkj4SewQWwH18vb5tOrFIMYsEfS04Ke8kjGJwYhSgh733hMd2Wozka1XmBiExhiNcB2MX+SjX7+Xf/7sLSfF38s1DlhGHiB5c6VkfHTQXrgKk/6ZjvDHifVOLFnUsNmYkc2QGbI/q3Sq7z6F49M5/TQc0kvgTzcmSEys0om3CexHGEKYfFZJUUuALEnubk1y1Yc28dv/maOaGWRgchQnk1R+XR8pXYQUCCkBTb2lCUTWvgYV27/n+pDJg5PpdY6mYU4apnUEX6slwh4tEfoYiK3g67b9EAGYJug6qCZGNUEaQpHh3R+5gYeOTiOlQCUB8XLNBi2rkchOg9rYUAmhYmv9tUxeRqffPhGUuGWzPsNT1mrG7ZPTgTqdOUl7AXNy52Za6NG9IFt3oIPphyMmScM6Hvg5tOcgBhy0u4Z3fTHDVx6o8L5X+5y/awSmI4I4CUzBKkAcMlvXIPPWCiNsPDF7FzRnYeQskAXQYargllbSDsQx9vq0TkGbTgDduebEaOjQWn4dJ9eikNIgjQKjcXyfIFYcPNjkO3fvZ3LdOFrrboFsOcYBy2smOLnP4yUPoaLk8uPkZsvEIitbcPI8GN4Eft5afmTPuvdZ8w4ESsUD6e4ykfIUOmlNTgTCguMUBEr3BnWUIGpbD5HNY7wMIudgNq3iu0cyPP0PZvin18KznraWzExAuxkgRdJ6HcYcWzQgskhp0MZYwWzNQ7MCQQzD2yE/mrRFpLJW6N41dj5rlUCbOHktMQJlWy10BAQIEyNMjEPEOVtW84RztrJ1coKxwTzFjKRQyNMKIlqtkEvPPx0ARzonzWGvKMBPOBDetnaYsYEsR8pRD8nppD04almrOzQJXiERftFfzSUd1Ar6EuencuVGJ4G26rU6CLeb4bHWUqYQZaodWSsroK06xDEmk0M4ErmhxOyMy4v/9ATvrRzjl16wjiyaVq1FLu9QrYTcc9yBbAGjo16rtHDBG7Rwq3rUtlNImcQ1ulO+Tv4/CW47iQJiMCFCBRgdYpQVeteBzetGuPKiHVx+zmbOO2OK8ZFBSoUsjvPwIqKU7uujWoFAj9M5b8ck60byHF2YtWX7jnWPWhYbj26xKci43V8HEiLVh3+K+kGnSSwNf1THQiaNcFKClNbgxwG63YIgSGCDA14W8nmkl0ELaWcUhEzig6Z9Kj+PNhpGczRq6/jlvz5ONTjCm148gddSIGH/bMRXjxWg5GKiZhLv1BPB7qREnVRas4P1o15WTCj7oWLQIZIQHbUwJsQREVPrhrjmwu1cd80FXHzODrLZzA8XQEqxrPH/slWAXD7P+GAGEYcYo61w6cimB3OrwcknAtlv3HsZkyWwyiwpIHXhUCfVmnSKSZuPN1ETXZ6F1iJuNmKi6JH17NNVWjA9LdD+MIxMgJuxguplIDYWEgkXg29hSMnDqAl+8x+O48tpXvvKTeAbvvEf0zSrAzCkoK1s+7Rq2I5Sx7XXmSnavx0nCqgSDK861j8CHSKIMSpEmwBJyMU7J3neVefy0qdfxMTqVQ9TJP7hxx5XYoDH8Vy4bT2fu+VedFS3mRZ/GLySzQjNHoDhdeB7Voi78GdJP3+3eJUqaBmRwvuxfcxxLcyJGpj5Y9CeZ2q9z+7dY5w+tYqJkaItWBlNvRWw99A8X7z9KAePlGHtVtvuLF0QOasAqg2uA0hMECNKDoY1vPlDM4jsNGvGHP7sqxqGhpLrjK03i0Or3NKxipUbTmBP3MvnqyQVbNo2pakjjIlAKi45e5LXv/BynvGksykW8o9Z4B9NOZZzT9CyVYDd2zfiRHViLaE4Cd6wbXjDQFiH2gIMru7H+Ok0JinhT1dJO63MSaVZOC5Gx7BwDGpHOGOjx7Mu3s7OHetZMzyE7/kEsSbWmlhp1qDZunGCi3as5e//63vcev/9sHEnws/ZrlXH66VuvZx1NkojSg6Bt5o3/GMZT7dpZ4ZhyIVQJcW5VhJbZOyo8OA4ZEqJ9U8yON20ZttOjJkAdEDG0bz06bt595texOjQQBe/d2anHy39/GjCvZxngpdZENzLNpy9dZzVg3kOt0chM2IDXi/bSz0269Bu2pz50oLX0ipOOvHTzZgIhOdigiYc38/IQJWX//xWLt+1jaHBQYR0UUagpIPvOfjCoJSi3Y6oN2F0ZIw3XncBf/SR2/jBsSOIjdswMulc1cJWqR0v6fAEE2mkL1GDIygFMiPt+GVHuKO6DYABsiNQ2pC8Ht3zWDoA1UKKAB23gDZrhn3+5I0v5mXPvhywff02eyN/JGu/ogA/I2diYoLNp5/HkftbmEzBWthQI6QGL4txAluQ8jL9ll6IJd2dSzJASX5cOqDDKpx4gIu3SV5/7eVMbVyPkR5ID8/zyWQ8HEcmaXXrBcJQUchnKVdrjK0a5zeu3cVvv/9uTpTLMLwqmQzzeq0aforVQhlwJDgCrVKV27gNYc3m8osjMLbTtkuopNbRyT7pCKEb6KiGY1o85eLT+au3vYrNGzqTXfoxC/4P4wl+nBQ0KwrwGM/sYo1DdReTLSD8PGZxFspHMdlhGF6LcDM2QFYqBWtSzV9duJP6usP/4wl0fQHm93PtJcO8/Bm7GB5ehXZyFIt5shkf13Usr44BbTRaG5xYI7CpRyEMtXqDzRvW8awnzPIPNyxCOGyzQjrxAp32hI7QCNFrchPYn9HYofmgAsWNMH6+nRtW7cSbaKtQSoIKMK15pG5w7VN3874/eC0DpeKPRTgfiydY8QCPEwwC+MSNezk410LkRzDzFTZwmFc+a5hbDkR8dd88pjSUBIW6T7i7E14ITkoHCWGHVepzMLuPX3rKOl781PNx/AKZXInBgSLZrI8UCeGtMWitULrXB6Nx0MZDG4M2EEZtrj5/ii/84B4ON+owOGiVQLknTWP1xSimd00QweiZtvrrZGyVuxuwCxtg+wM2z68W2TLi8LH3/ia2ce6HD1Qf7meXa7HrEVO5y+li0zflhnvnLOyJNVQPc92FQ/zCMy/hN67dzWCuBa0waXPW/bQhfTw9/Q1kQgpMowKzD/BLT1nPy55xEW52kMHhYcZGBinkM/iug+tKmwMXEikdHMdJ2rQlTvLhOi4Zz0M6HmMjg5w9VbIZIEga5mRK6EUvFXtShVpDaRJW77IWv9PCrVMFN7Dt14W1MLabxcJu/uQTd/Kte470TXx1ycIeRYgfDtM/1t9b8QA/4dNsNtk/FyG8PMzMsGO4zoU7d7DQznDWjgmefdE8//yFaRgZtAogZYqNzfRDoG77sMCEDZjex7VPGuMVz7oI4RcZHhpkoFjA81wcYUmjrPGVSQHK0h4KLRHC2N6ZhElOCGEVxMtw9qZhPntXpWfVRao6LZZM44i0MogE8iT5fbF0Ci2t0BrhDzCnBL/94bsZHbiHqdV5Ltk+wnWXTvGEs6ZOEuYORfyPQ6BXINDjdL67f4YT5TZaS2jMc+65RbZNbcIrlBhfNcRTL9zMP3/+qBUux5w66yNTwi+xBaQTB9h9mserf/5i8qVhsvk8Q6VE+KXTnZIyCXbvyKIw1qBLbSytuRBJoVbavnkl2TReRLqLlkbRSY1xCpYINT1o1Bn57PTw9zXuif7fs4sAMCYGIdEyx2xDM3ugxXceeJC/+dw+dm0u8OLLt/Csi7eyaWIUIe31hVGM5zr833iWGQSyn/eciJitK0wY4Is6O7euYWhwhFw2i+e67Ni8moFVWZtD77Q7LJ0d6VhdIRGuhNkjjOQWeMMLLmDV6GoyuQJDA0V8z0sIbk2Ka9T+EbH0j3amE1NzATZOFgyXMhRy2JhEJi0SnMJidhr20rUK3WlzSNKindYHlbQ8pAP7dGZLSHCyaK9E6Axw64Mxb/zAnex6/Sd49Xs+zc13PQjYIaOlNuKxWvflPhS/LFcknai0McKBdos1ecOZU2sxjkvGdwHBxFiBbWsH7WhkhyqlEwekB9dFgvubNWge4zXPPJPTpjaCm2WolMfzPKSUXcG3RtcgEClmZdH7N4XrBclmlkQR8r7HaMGzCpDmCzJiCVePSV1jogwdktxOdbo775z8rT6WB3op3g7EE9KOUnoFjD9EOR7k7792nCvf8h/82p/+F/c+eDyBa4/c1Xmqx5d7ULysFEAkzVflWtPy6ocBI4M+a1cPAwLfkShtKOZ8NqwZTKxtejpL9HsBaXE7Jx7imnMGufKi0zFOlsGBAtmM12WF7mL6rlCJVCZJIIRMHjInKZmUDgaJ5zlWAZY253WG3DvKIlPxAalRzc64Znd+V53M6tCFSUto10lnvhxwcpAZJvbGeN9Xj3LNW6/nTz9+A1rFD0uB3pn7XYFAP00FSD5XGoFVgDhg3WiOUqGAdCSO4xBGimLGZdu6AdAda9bp0+8fJBdSosvzjA40+fkrdlAoDlIsFSjkfZwkzy+7wpkeeUwgUOJBbJNcrxVapNjjRFKplQiKWacf3ghzEoQirWRdxgadqmHonsAb1f/9ziSbXtryIfrHJRMaOOHncAqjHGkVecsHv88z3vovfP/eg1YwEuaHH3etYEUBfgwp0Fq7ndzkiA0jOXL5PJ5nxwnDWCN9h+2Tg+B2rPUSeJHAFROGUDvOM3av5rwdm3BzeUYG8zjS6bFPCJHC/B14I/rSir20oUwUQaSqoz0Z7OudTw/Q9M0hLCXc1SkCXHpK0OH/0aeoJaRnlrs0Kuk/L7pDYbESONkBdHY1X7ynzhVv+jh/8+83p5Tgf67wL9sYwGJnjUPMqsEcruvhSJFQ59gbP7luEDnkY8K4l+3s4xIyUF1g02jEs5+4HT8/yMhggYznJnAn7T1EV7DSfwohiJVGujb2iJN1SOmleaYDHwTE2un3REL0Z4PSnapdKCNOFvLO3LNItXh0yKr64M8SbyCW1EM63KexQbgeJruKKiO8/u++yRv//NNgbEo3Vrpv19pSgV/OccCyVIBcxgMNrlAMFLKJ5RXdLY0qVGwZL3HOeAGaIcIR/VZWCDu0Xj/B5TuH2bJxLX4mSyGbRcpOkasT3JoU+bJIZMbiYa01gyPDFIslhoaHKJSKKBUnhtlacJMIr9GaZqh7W2T00hliTrFLgCUEXUs8glH9NQStTs4I6SUkvkueto8hUUhEdhCVWcVffuZeXvaH/0qsFJ7rdJXgVEWy5VwhXlasEB3ZGBsZBgGONOSyHlJ0uP0lQkqCIGbjaJ7tGwehFaWYdUQvJRrUKWSaXLNrE24mT6mYw3GkZWVI/ddfazIpxCIYHRvnwIMH+F+vfDm//da30Gg2KZZKaKWsgqRkWSlDPUhdQ1/FN52lWsLv0xXUJB4QHVhjlvD8mF7znFFL9hiYfv6jpV+bnqIYAyJTQGdH+eiND/HKd/47xmhcR3bXrf5Pyggtq0KYXd0jGCoVkkKUIePZoWyZWGshJWGkyA8UOf/MdXz860dRKlUME1ghaSxy3pYS2yYn8Pws2YyP08n09PVOpPL7ku6yu8HhYWr1Gte94Fru3bsHgHa7xTv++E8oFIpUKlXAJEuzDaHSNEJ6I5eGfmKrk0ieRT+kESS9P7pHicgSxUAkNCwphrcOiW5ntwFLeEXFyV8bLWyDIYKP3/ggI3/2Sf7iTT/fxwz9wy7YXvEAPx4NAGA0YwmxhJS2gpkSJAEJV43L7jPXMzicwwRxisszgT/hIlectY6BgSEK+QyupJfvT7VHLK2gCQGO4+L5Hv96/Sco5QucvWs3uBk+9JGPcPvt32ZgcBAhRRd9GKNphYpye0nnZzodupRl7hSv2w64pwbfe4n/Hssbpxj51KnnM0vSwemmu/QyGS0QmTzKH+VvPruXP/3IV04p7On/X8kCPU7nzIkCq0q+tfwd2UmwitaaoaFBjj50gPf83q9TqR6F4pAVGplw97drrB427Nq+jkKxSCbj2Vy/6KD8ThbHtjX3pxIFjuswOzvHl7/wRT75X//By1/yIogDonabb918MxqB47gYk7CqCUGtpWm0k+Bbp6BMF5aJfqXoEnLpHtRBJ6S2KbKrpWnSjvKmib04xQ5iQT80Sq946rKrC8gNoHJj/Pb7b+brt+/pZodOFQyveICfeBxgP581Ocz4cAEtHMJY2Q0uCRxwXBfH8/m3//wMn/63T+C0D4Hv9wbJjYZWld1TQ6xbM4Lr+TiOg+jcVClsbt+AQPalJ0Wy79fzMtQbDe5/YB9/8zd/yw033tSVmgcfPICKwsQDWKFypeFEObSsg+ni1lL68j62uvSAjkrRGyb/T4r3B3Ny3MASwe/SPKaCcNWZf5A4rsBxJNJN4b2OUuaGiDNjvOa9n2d2fiFBVPok7L9Cjfg4nZGBAoMFH2MEUayRIunmNJDP5Tly5DDrN27kDa9+FWr/DXZyysv3rF7c4NzNYwwWS7YhTHRaG6QNfA0YkZBRid6iX200xhgyGZ9ypcLMwiLvfOc7+cznv0h2aAQQzM/MEEbtRHZtnIIxHFqIkt1fOmXZzckZHpYu30tZ9y4LnUrRGXYUIg2NUrxHfQzQSzhPpaWM17N11Ikqaq6OXmjbdU0piiOEC7lR7psz/P4Hb+h6geWO/5ehB+hZmK0TRYxwktSiTTPa2RCHVqvNgwf2Uw8FzO5BVPbZ5XKOA3Eb6YdsXT9CvljAdd1uBkl22xxEarfwUsY3G2jPzUzTbrXBz5IrFNBagZA0G3WiMOpgCCQGjeHYouqx13VTlUvboEW/5U9TwAuTwB+VYoFINch1Mj/dmsApKsRJD5HoPMdCE2oNLts5xFuum+J3XzzF088rWpqZRpwKFYydj86N8oGv3MvXvrPXliO0WdkQ87jGwKl883mbV/FR4bFYD8Ho1GpnyeDgEJ/+z09x4803Usr7xPv/g9bIGaAcOHGEbasN2ybHyWSzOK5rq76dHJMQKZHsBaem0/OTBJ/lSpUwjuxjnfZkKdBRTBQrTDcDpGmHMYfmopPhiREpIi6W7CZIeQiRDpBTHkGY3iomUnMFRvUCX9FPByMdgQ4VzDTYtn2IP3jVWVx13mp8oQhaAXOLo1zz3aO8/bPzlJsSitK+v1rg5Uq0ojp/9e+3ctWu03AdB6V1tzFwOdaKGp0GAAAgAElEQVQD5HLV3PM2DyEcj4V6BEbZwY6EIc51HTK5nBXh/DDezB2MHroe6segeoQXPvl0tmzeCAjcDjVIIkSm+286H2ROWksUtO3uYJmQZdnWfcf+ltZJAGxwJMxVAk7MhyenOk9awkHCKB33C3xfs1uaySv12aTqAn2V4N7vOC7oZgjTFa576ka+9J6reeE1U2QdSbmuWGgZhJvl584d5c1PLoKMLZlXcn2x1hh/kM985zCfvukHAMRKrwTBj28xzErP7s0jrBoqMV0NbQuC0Risxc34PoODAzZwdV1qAayr3MYfPmeUv/zdZ/LK516B5+fxPc8yMXd792UqQdi78R0QZC26/W4ul8FznL70qIAupBJJzJDzJfc8VGehqmxOvmPB+9ilTT8lu0lBGLG09bnD779kd0H395Pf7SuUKRwJKoihUud///LZfOKdV7FxNMv8bJNGW5HxHbKeSzs2zDU0uyYzXLrJgVZvpsIog+NliZwS//XN+wBFxnNXFOCncQpZj93bxjhai2k0m932njiOyefzTKydAAyuFBjhMrPY4FmXbOW1r3wuAwODuK6L67rdLs7+vh/6BlKsnTV9KGVwYAjf95MUuvUgRitKxSLScVDKtkForbl1Xx2tBLhLoExHYLuLt3V/tscs2ePFEmzfFyvoVO9PhxDXxgZCalQ9gHKNd71hN3/w2icRtWMWyi0MAkfaBIJSGh3HBGGE1prVxc5etZ5yamPAK/G52w9y976jDxujrSjA4xAIP+fCTVRCh8VqE5k0noVhiJCSrVu3Ji9PIByHOFYsLNYAWzzznF4uO/0vSbW5f/1Qf2UpjmNWrVpFPpuz45HC5o9cYHz1ahzXI4xi8r7gyGyV+x9qpoLYVKamG+Sq/nz/EgHuCXcy/SWWLMoT6bgirTzK1rcaEcwv8K7Xnc9bXn4xQa1BpdbGcQRCGLTRxEoRhiGtZoswaBNFEeWm6g3m61Sywc0w1zTsPTi9bAV/2SpAOutwwY51RLHh+GIDKSw/TxxbPs/NU1Nki3mM0XavmISo3UoCQdkVGpHuv+nEAcKkgliT1ARsSlMICMKQ8bUTDA8O2I5JIfBcSV7A2jXjICTtICLna761d4Ejs6ElwEoLszD9PT5LLf/SCq+Je+lO9JK253Shq5cdEsJg2hHML/K7r+kIf416K8BxbKVaKY2KY1qtgEqtzvxiGR02qYWSO44r8FN9Q0p3dynEMsdNd+xDJ+/3ch2YWZa0KJ0izBlTQ5y5fTMH5wIcjL0/WmO0Zmpqisn162k262T9DBkhObkVcqnl6u8B6s6lJNkVk3AKRWHI8PAwG9euxcHgOQ4Z6TLgOExumiJUCt8xlGshN/7AFo6kLyH2IM7YD+WDcfpz+N3qbaryS6rhrYPt0zue0hBJ9LiPuvPyxxd4/S/s5A9/7VKiRpNaM8SRsqvcSilaQUS13mB6dgEV1BksZvjS3SHzs5GlFlKpofxEAQwuN915iMVaY1l7gWUZA3SKMC5w5VnreGi+TaGQZ2x8NcOrRgjDkNO2ncbFF1xAu97AcyR5z2NoaLhX0+0MuqQmuTqW1IpWv5ClB7niOCLr+1x22aV4YUjR88nGMRtWjbL5zJ20g5CiH/PNu49z2/0tyAyimxLiiIJpMiAaZE0AkYAwC1rimLjXzozqZ3wm+dApDyF0d41RLyZIqsIiWbM0Xea6Z23lvW+5mjiIqNaaONIG81pr4iii1W6zUK5y/MQsJmoyNJjlE7fX+ZebZyHTCbg7UEz1vI9wODDTpN4Kl3UM4C4n6995g/fdfz9f/OKXuPXmm3jwyDGOV0LaD9zA2Wefw5Oe9ETOPW8XAM94+tO5/uMfo7qwwOTUJOs3TvbC2YTWJD180rFtvbx5ag9u5z9tBagVRjznBddx/fvfz73791MErnn1qxldvwnTrjJXbvB/Pl+GwOPcsaOcXpph9/AsE34bB0MtdLirNsq3yhv5dnUdSvpIt2n5QUkJf3fTS7LwojvWKVLeIF1B1iAd9FyNXWeO8N7fuQYhBOVqE8eRSSeEJo4V7XbIQrnG8ekZfBHjZTP805eP8tFbZsDzbQU4SpjncKy4SGnrF8Kh0Y44PjPP5NqxlU3xP+mjtcFxBLfc/E1++Rd/hdmFOS64YDfPeebTiaOQPfc/wMc++hH+8X3vY8vWbbz4pS/hmc99Dq979Wt491/+BRc98QlMTE4Rtpup7I7oQf/ENxjT25bex5puTCrjqKnMzzGxcSN//Ld/x3998AOMjY3x9Ff8IkK1qbZD/uRTxyks3Ms7rpjhmZOLbBjzIJsBk0t2Gbcx9X0cmrubTx5ewzsOPJG59gjSr6I7E1/EPSXoeAU6XmvJ4mutwbFVarPYoFiQ/N3bnsrE2AhzJ+ZwXYkyBq2s8DdbAYuVKiemZyh6hvmG5j2fvIdb71mAUgacCMJOitgB4YH2LQ9pdwey4ND0AhevVIJ/8sdxJLVGg/e//wNUmw3KlRrXvuD5vPJVv9j9mVazyS233MJHP/xhfvWXf4mLn3Qpl116KR/70Id55rOfidaKdhgjHS8Jbq1FNZ38f5f4yiTypROsLDBJ2b8Te8axYvbEcXbuvpAd5+6iHUaU5+dZbCpu/t6DbD/xSd73vAU2bB2GwTMJsiPgejby0DE6jojqNUYGjvDa0gNsyHyOX7zryVSiIYQTYbqwowOBkukvnWKMQPVnr4TBBArqdd7xW0/jgrM3UZ5bxHEEKknJRrGi3mixsFBmcXGegYxk33SN3//Idzh8pALDOatoYSot3KFVcXJgMkCuu3a11Wyd0kuvKMBP4CwslqnHMYVSkYEw4I/e9SdUqjVe9apfpDRQIpfP8+Srr+bJV1/NU6//BC984Yv46te/wQue/zzOvWAXO4aG0bgoZRBusoA6CpAq6FKeaGOstSc9uaiTOpPpTnrZopth7sQxYqVpBTF15bFwaD8XHf4gV1wVIDftpD2wEa9QIuP63Z1eRiuEjskMNokLOSr4PNfsYX/r27x1z2UY14DoLPQO6bVBQ6r/u+upOq3VBglzZZ77tNN43UueQLNaI441OvEQQRjSaLWZW6gyNztDxjXcft887/z4t1hYbOEN5TFxHYzozsnoxCMa6UJcx8is3WuMwkR1VBStxACP15lcv46Ln3ApBw8dxQjB9PQ0b/2d/8373/8BrrrqKq684grO3LmTNRMTxLFi1fgEtUaDj/zLP/O1r3+Df3z7L/Nzu1Yxc+AIZAYxa89DbDgfmSki4zYyatqAN6kMdzo6DdZ6Kt0rbimliKIIE4cESlJXPhy/i/MOfYwtZ3iw7Qno0hqyXhYjXMsl6tjKs+hE1VEBV7iUlKIV1njp1GGuP3yQ71bWgx+DChLL3ymIie4uYXBSSStjFaPaYGKyyJ+85SkIaajVm0nRWROGEdVag8XFCjNzs/iuIDRZvrtvmudesI1tp29kZKhELuPjey5xrIiUptFsMVeucfDoDPuOLnBkrsGJuRniOCQnI6Y2rF7WHkCYZZK8VUrjOJKDR47z9ne/lz133cHCzDHq1SqVSoUwDPEdl+HhITuA0mjgZbM4CVnW3LFZztk0yFf++uWsGspQfmAfutUgXrWTePNTMONn4OQGcdCIuAUqtB2eCexRWqO0TrKUhkgZ2krQjASt8iz5wzeyuXoLo2uHEZPnYAqjSfCYNNhJIG5Cs4YKIky2hDswgoljRPU41fvvJnf0Ht7yrTHee/9ZkDFAq5d5EQIhXTASo5J1liJZEu64oF2Yr/HP734OL3v+hUwfnSeMFNpookhTqbWYX1hgZuYEUsD2bVs465yd+P4PZwNnZmb4yi3f58bb7ua0TWt57f96Ab7vLdstMctGAaC3Kf7I8Rne/5F/5YYbb6RZK1NZmLFtyEFAsxWgtMLPZOz2FgzCCIJYY8KYP37Rbn7tTS/CSEk0eww9f4ioWqGVWUs4ejZ61Q7EwATGyyV9nwKjlN0DYDRaY4U/CIjLs2QW7mN07puMy+NkN50OY5vt9kZj16lalxISzh5m9t491BYWGcw7lAo+ZnQDxS3nIkxMcOAHsO8OPnAH/Np3d4KfA9m2WFtK247QNnY5XsZLLfdwcDI51NEKz3naDj72rmupVlvUGi2MMbSDiHq9xWK5zOzsLFrFnHnmDi68aNePMUGhHxPt+goE+u9qa+Jm10+s5m1v/nWuuPQJfO2mb3LLt26l1agxt7CIX6ti4gAVh5adIcnmtLVgzdgoTn2Ru79wAzue82z8NZtRQ2vw2lWys/sJjn6K9qEvEefXofKrwR8kdEoomcFIH61jUG289iK51hy54ATDcp78+mEYuxLyQ+BkbfpQqG6gqsrHWdh7Nw/tPcqJqmYhkowUDWetnmaD65LduM2OKivDeCYmK9u0tYdwDCYIYXYBchlWr1tDIxY0ZuuQz0M+gyMdVKVNcSzL6156IVEYcXy2Aiam3Q5pNBpUKhVm5+bAxOzYsYMLLtzVV1BcSvB1yvf+EShRlvNZlq18Sikcx+Gyi3Zx0Xk72fv0p3DHXffwja9/mQeOLXJ8oQGtOqbVRAhNoBQbhiUv2jnIGVNF9u+5n9LUnUyefx4iijCFUZziCPmJGrnKcUxjAR2XUQ2NiuIkA2SLS44rcT2JV3QRa4Zg8DRMZjDVPyRJT32ZsImOYwYmp9i5ag3jDx7h8986xO3HDW4gGd82S3Z8HBVEhKHBRSGJQWhMvQGL8zz7abt59ct+jrGxYRYXK3z9lnv4q+u/T62l0SUfWnV+8drdbF8/yJ17DxKFAa12m0a9RqNRp1KrYrRiy6YpnvjEi5MqsXnYgfZTYfml30//THp54YoCPC4pUadbzcz4PueeuZ1zz9zO884b4KHbPsO+RThUMdRaEYvTcxRUk7NXCdYXBK3Y4GUzHPn+Hazdthkvn0e1m7a4miki1pxmmUQ62xnjINm0nkARx7VritysXZiHsPUo0Susm05Dm4pBKdziMN7QKMwf48i9bVblBBdsX0Wz3mChEjEQNombDeIwphYmfysMobrA299yHW9740sBqCxUWMy7bHnebkp5yds//APaszU2Tha5Zvc49927n3KtjjCaIAyo1+vMl+cJo4gNExNcdvmluJ7bJ/w/qtCe6vdW0qCPY1UYY3BSBK5CCAa3X81Zc9/njPJh3GwWBkZ54Dt1Zg5VkL5PO7a7cQvFHEGlwvwD+1hzzpnIpMprEyqWzRnpIaSL8fIInG7V1T6d00+xI1O7Akzn7yirCI6HcCS0yhy6+37uuGeaTZvGuejKM5k5eAQtBKpaobm4SBxGHGoUaMYS6mV+/7eu422vfynEMSfmFqjVmswvVGi3Glx9wUY+/+1D3PTZvTztueezcSRLLRRs2LAW17U7yoIgZG5+kcXyIttP28rg8NBjgi6PRYhPRY+yAoF+0jHAKXDo0hvR3vIs6l/9PwiaDIQRju8SGIkJFdKReNIW1dycz/yB/YyftslabpOebFJJVdjSpxuUfZ4OM4TQKZZFSa+DyDbOdXcFSN/CocYsJ+78Ad+7bT8Ta4a54MqzYWCQgTVtJIZgsUx5rkY2lnxrPgczFZ7+9HP5f1//UkwcMb9Q7jJVa20LWfVyje1rJKWnn85vvuKpTG1YS2QEWhu0UrbLU2uGR0ap1urUqg0eOniYyU0bfqRB9qW/cypuoBUI9HhY/oe5KZ0bkFuzg/j0Z1C/81NUFhtkMi75vEcQhAnprUAYjeNK2uUKzZlpCuOrMVolRSY73C6kQ6cRTiS7wLr8oybB+sJJLH6vD6fHeO4gZAyLRzl4+/e4964jrJ1ay3mXnY23agzVaOCXSsTVMuVjM5hWwJHA45tHDZQ8fufVzwagXKkgHYc4DGm3A4KgTb1eYX72BC/9uQu54MLdFDIZKtUaWutesU7ZuoVKNsi0gzZ77rmPfC7L2PjYj5zpeSz3ZUUBfoIeIP2Gn7zEQSOEpLTreUTVYwQHbkO6LqWiDyqyzII2+ZtAGEVjdprC6BBGqQTa2NSlSYbKuzVRoRNPIdMpqR7JVHePQBIKqJD6g/fz0O0/oF4NOP3CM5g8ZwfkiphWEykUxCHNEyc4cbzMuKf514NZ5mc1uy7fyJMuPgsd2DYDoxRxFBMEAfVqlUa1zAW7zuP888/D6Jj5hcVunkwlSqC1Io41YRQTK4MQknYY8sADBxgbH/uhvcAjbY1ZGhSvKMBP2AOcCgp1m9gSlrXiRa8grCxgZu8jl3WI2q4tZPX+Go4xhJUyqllGeDlEh7ZEJJ6AFONCQnPSo0iJewszEtY4Y2JMq0F9ZprFhw7TnJ2nODLC5ounyK0eAyPRrQYibmPaDRpHj3DowDR+HHFMeXzkvizEMc9+0k5A0my3ExZqRasdUG80KJfn2X7aFs4+92zKlSpxHOO6Lu0gotVq0WoHRGFIHMUopWi2A2KlMEYQx5pyuUq9VqdYKnaF9rEowyPtDl6JAX6KUOiUPJVa4+eKDF/zG9Ru+GvM0TvJZlxUrNFGJYxt1oLHzTphZR6vOIh0M0m7ggvSsU1y6Q0xfbQOieJpgwpbxNUqQXmRdqWCjgKK+Tzju88iOzpqmekihW7XIGwSN2s0jx3l8IEZqpWAdUMu77gtz/3THuQDzpxaYyORWGG0JoxjGo0mMzMnGCjlOW37dqqVKlEYoIxgZnaecrlCq9kgCkNUHKO0Io5jWq029WaL2GiymRyOHKXdblFMNsj/KMK/3Mmw/kcoQNpyLc1pi2RgJpMv4j7lTTRu/SD6nq/SqreIlUQmVV2DQYUxUbmCg8H4OZxM1rb7Sg8cqwSkqf+M5eIxSITWEIfQbEBQJ5PzyQ+tw8lmkJmMXV6tNbrRwIQtVKOGqtVozExz5PAcrXrE1JjH+/bm+PjeDOQUNEVCNRJ3e47CMKJcLtOo1Th9x2koLSiXywStNnNzcxitGBwYYHzjOgqFAo5rt+UopQiCkJnZOfbct5cHHjxIPu+wauSxZYMeLu//cL+zkgb9KcUCD1e0EULguB4DT/pl3IHVzH/zowTNFrE23U7HOAhoL1SQaNxcCCpEehmE6yFcz/bzCJl+chsnJ5hbCHAKBdxSiR5/j8LEMYRtdBiim02iWoV2pUpltsL0dJksii1jLn97X4E/+JYPWQdyQyAl+6ebENtepE778vzCPHEcI70s09NznDh+hNmZE0xu2MjpZ5zB0NCgXdqtdTfWEQlJ79TUZs4951y++73b+cBnb2P4xr0848qz7Rplcer3dOl5uOV5YhmTYi1bBXis7tp0qAyFJH/2c3BHpli85cM0j+0jaAdoZQjbIfVyDaNivGwTL5/FzWVxMlmcjI9wXJCuHTShF+gmHNK2emA61P2WkU3HISYIUGFAVG/SrtSoVxpUyk2ajTYTRY32Pf6/7w3xd3eUoFRClIYwwoe4zDfvPEhloUyt0aTZiiiXK8wvzFGpV5mdnWVhfpaZmeOcf955XHThhRijaTQaXV7T3kbIXkuz47hcctFFPLDg8bYPfJWdW8aZ3DiOTuaHH80bPJLV/3EU1n5q8mKWGZh7tJt0yhuQBMZCCHS7RuUHn6X2/U8R1ipEgaUsdB2B5zq4voufy+Bls3hZH+m7OK6HcOweAuEkuX+dLM9O4gCjFDqO0FFMHEQEzTatZptmPSBsh0gVUHAUWV9yS3mQP7trDd88MQKlItKX6DiEKIJag9XDWa7/o2sZG8jw0NFpFhcXuf/gfiKl2LR2PfsefIALd13AVZdfgYMiiuOE5FcmW1STYL0L3QxKaerNkOGcw+999DaOzTW5/o9elqQ4DUvfth9FmJejB1hWCvBIgdjDufC+x7WyFh2Iysdp7/ki1bu/RFSvEkcRJtYoZTtOHc/FdW3vj+PKLoV6Z++wSSgNjbE8QSpWRLEmDDVBEKPjGB9FxlHkfQmez/fqI3zwwHo+dWSMOBIIv4mIGpY0S/rJBUuYq3PFJRt5169ewsz8ArNz89RbLVzHVqBjpTjr9LMYHR4ml8ugNQRBQByHRFFIrDSO4+BnMmQzWTKZLFLAYr2Fq1rcfaTBb3z4+/z2C87mDS+8/CRL/sMEucu9IrxsFeDR4oJHulHpmxTX52jvu5Ho0O20546gmhXi0GJ4EyuCqLNxJr1fIhmMSeYEYq1RsUEKyHkwnDX4nqQpsszpAb5fHeX6I2PccGwQ2gr0HOgyCB+8UTthpY3t/myGEEQ89ZI1/Mo16ymVSijjoJTmwEMHODZ7nDM2b8dzHJxkp0EQtEg2ulq+XSEQ0kFrOwpZKA2Qz+cJQoVjAiqBy9s+s8CBo4f5wV9fx1mnTf3YClorCvATVoBTdSE+Frf9aJZKG1Aze4iO3YlbO4xTOUJ74Si+iJFaEUeKKNREkSKOE+qRhFpFJq35sXGoa4/9zTx3LRa5dabIzbMlTlQzENUgnAHaCK+Aya4DfzhFYgWmEZATIe/+9Uv4pZ+/iHvu2cd9999PrBTtoM1ivc7hmROcuXUHQsUcPnqIdWvWMLVhkomJNQwMDpHP55FSopSNC44ePcLde/cyX65QKI6wZWKI/bUSb/tKRKu5wLPO8PnUO14CjreiAMsRBv04LNepbto/fvQ/uenWbzNZbLMhHzHqBxQdRd6J8YRlb44iQys0LAaSww2X/YuS/eUMt804VGrCsj+IFjgNOw/sj0NuLWSGrT9RgSWdEgYiDfU673ndbl7/ooupNDTKSGrVKgsL8ygNnp/ltju+w7HjR9i6YZKhwUHOP28XE2vGLeWJMWhlMNiKuJCWqPf4saN86etf44H9D3DRzu18bE+Jj34nQhQNXv04//Aru3n5sy55zB70f1IMsKx7gX4UwX80T/DQ8QXe8k838ZlbDtGsD+C7HmNeiEeGQEEYC6TSxDoAQkQc0YoMrUhCLECE4MQIGUJGYNwByE5CdhV4A8lCl1bCqiBtW4YE02hz7dWbeN7l2zgx30I6Ho5jGBgoUiwWqNWbhEHEQDbHoTBm89RpTG3cwFApT6NeTbFbi261Wyfzy6tGV/OcZz6bW2/8Mjc+0OI/90TgGAwuoTfIn33qBzz70jMZGhrsTt39MPBzpRD2M1ATSEOgR0/nmS4rdCIq0D7Gx26a53c+vhdRPcgvbJ7lstV11uUDfKkJY1hsSw7VXO6Y9vncQ4LpcgZUFjwFuTauURgh0CKP8dZDbpWFOTKX1AcCILITY0aAjBHCwYSa0miOZ1w0QRjEaGEYyFj+UqM0YRAQhW3KlSqlfIEnnH8BURiysDhPNuuQ8b1u856NTzqxiSEKIxbKxxgfzrF2yzl86JN30mwKRN7+bccvcs90lX/87Hd580uv6nJu/ShGZaUQ9jjm+H8kr7CkUqwaizT234a590u8/dYCHziynpdOHuYNlxxncyGyeF/rbp+RwNh9ZDuh3IYvH3T58J4CXztWwshh1KAHmWGMPwYynwQWsV3pmjC2mZYD7QgcCb6Hl48IWyGnbSwwmjcsLFYZGPHIZiSulHYYP44TVjfN0PAwcRRz/NghcjmPWGlkrJPt9aBUTBjGtNoBrXZAEERWGdo+Bp/xgQzH5poY/CQr5hF5g/zLDffzoiefzfo1q4iVwnkMO8Aei8FZUYDHWSkezgoJknVCgKoeo3XgNhp7bkQsHOQ9eyf51NwwH7rwDp61YYFW6DJbtxmUWPVSnhhl+/KlIOtJfuEszUvOa/Gp2QzvuPt0vlfeDjnLM6QjBVGH0QGMkdCWnLaqyeXbqlTLbW7YP8B0UAQR4zmGZqPCbBQSG4EjDJ7rEIUhvufi+S6FfJ56vY6UPlEUcO+99xBFMUNDQ0jp2LaJKCSONO22bYzDgOf5OKJENivYlA/5frMK+WFAEocBQinuPVrhc7fs5VeefynGPLaZ36Xv80oM8DMQF5x0U6DbyBa3yuhDt1Dd8zWaiwvkGnN86MEpvj18GV944RG2uMNUFgaJ3DbSj5BxiIcG4eC4DsKTOJ6D4zlIRxC7Es+XPH9LyFXb7+JNN0R84P7t6IyDcGMr9MKBEGgJzl2/yCdfeZxNmzJwaI6v37yX1952Bnsa48y0Q6rNCBU0bEdn0MJxXcZGh8nmsmhtmFizmkOHAk5MTzO15TQOPXSA+++/l3yuQDabJ1fIgzE0///23jtMsqu+8/6cc++tHDr3TE8ezTSaoBnlgIxAYIEAGxOFiQYTXozX64RZv9i7Zpf12mtj89qsH17v64ABJx4QYDCIYEBC0giUw2hiT+jpnKq74k3nnPePc6unNZ4oZMmjrfM8Pd1dVVNddeuXw/fbqCOFQzqbI53JIKWLJyyj5tiibytPcQBIiH2EMAQmw5fv3c9bbrmcYrF4waPSnRDo36V3WFYB4unH8A9+m2j+BH6tgRvU+d5olrtLN/I3P59jXXk9UWUdpaEQE/uY2M4FYaKEF8AgjAJjWeqJ7FccRARL0JXy+auX/JBep8bH916HyRqEE9j1SiHB0fz6VaNs7GsRza3FeKu4ecsk/7XyAG+q3MKRcc1SkKK7GDI2O8FCZY7169ayZctmHClwHUEqlWb9+vUcOriP+blZ1qzbgB+EqChEa83i4gJGK7rKZfr6BvEyOeJY0WoFZFIOh2Z9Hh6tWliVyF8m0dbSASfN95+Y5pFDE7zoyhdcUGhzMY9Ey4tbwM91we39/si3CZ74PKY6RegHqFaTExN17pWX8d/fUmZdroaa9fHwESikK3HSDk7Ww8nlkKmMzRu0hkhjIo2J9TJSRCoFjVYK0xT84ZV7ePeGe8HPYfBsk8B18TIOXXEDpptIIqTnUvUG2OjO8Yq+g7z+pn760zWUW0IA49PjpDJZPC9NHCu8lIfRilKpxAtvfDEYxaEDe5mbmaBeXSL0m3SVi2zfvoMdOy5j1apVZNLphKopxktJvnj3KHElBCfJTXSC+68UCJcQj/v2jvJ/0nl+lm6v798AACAASURBVEGNRkiHKIpRB76Enn0cFYbEQYTf8FmaXWJvtcjrXnsJOwcaUImRjkjw92OL3WkijIkTdviEJ0tYVAjR5o7UEmEEQmkcGVNtOZSFwx/v2sNIq4u7KtsQXgXjQaSy3DHez09dsh+T9RGpHDLt4UeKD1ytuP4DL2fv/T9gfHqeVf2rAcjl89QbDQSarNG4jkMUBKxevYrXve6NTIxP0PJb5AsFSsUS6UwakERhSLXeJAgCms0Gg+U09x+Y57PfOwbZdII5yknCAwkogXIyfPuRo7zvZ+p0lQpP6/Pp0KQ+15bfmKQkCPrwlzEL+yAZVWjWGizMVzk4usTwzg28+FIJi40EEUIv06Mu7/4myMhCSJDJLJAQNpmWjn2sTL4EOI5hrpWjy1X8/vD3yboVjM4jHAWuw53zQ0wtGNy4kaxvQiqXZ/XwZTiOy/bLrmLrJZvRRtHfM0DKTTM/N0+j3iRWlgvZGEPgt0in02wdHmbnzp1sWLeeUqmEFILAb1FvNKnW6szNVegvpZlvan77c4+hWwpSImF8USv4yNoMNQ4PH5plsd4667U/bZHhIkWGuygV4KwjD0mlJx69BzV/MKmlK4J6jYWZCkePzZMa2srVN24DP0DHGiESc7hMh6QTLP6TxBgiWX8U0sEIuzHGMsWqRDgy0Q3DdCvHNYUpPjD0EOi0zUWcmNGgi4fmShDVUdogJWRzWVS6i/mFGiqK2bFzN9u370AbRb1eww8CpqdnWFyqE0YxWluYxiAIqDfqNBoNms0GjWadRrNFrd5ibmGRmdk5essZ5hqKX/rUvYycqELZs/mLOYVjDDsoiJDUI4e5Su2s1/5s4AQdBXgOPUH7d10dJzx2p616akXUarEwXWFipkH/FT/FSz/4ITLFAjqIk5VfveIDbLOunMThF4Bpr0Qus2InYLVPaagJBIqWMsSRw2t799OXW0SbHHgB1TjHnQurIPQtup0rcR2Dqs0TKk2z2SDwfYa3DLNj2zYqC3Ok0xmqtRrHjx9ncnqOWjPADxRBFBNGMX4YUW8FVBYbTEzNcWx0jMXKAusHuzg+0+C9f3QHjz05jsgrhGraxF7HtBnvT75n+/pDIxkZ+9fsj2ez7p0+wHNg/c/mBcLRe5aJ7VQroDq/xPRMna5LX8T17/kQ+PuJj9RwPTcBvEo6wyvph6SFHxfLbER6WVba3sCiS8h/lXSnUEwFWYbdOa4vTvC14DJSboPQcXisugq/foRUoYWWaRxhWDryCN72lxKqgFSqjuc5rN+wkbGxEywszDG4eg3Hjx1mcXGJUrmLfL5AOm09SxiGdhm+2SQIA/LZDKVSmS/c+SSf+OIPWVyKEd1FTLBkWV5kGmQGRNp+/O2l/mWCKMmJqcrTEvJOGfRZtvinHYxrTqEqIziuBBXh1+vMTVcpbL6Ga97zERQQTY6Q8YSF219Jktfm2pLJXLEhYVxfjq8SeBSR0AatDI/sWKgQEikFIQ49+OzITvE19zJiXEgZ9rW6eXwuzTV9dZomT6bURWP/Xnj0TgaufimT07MoYxgcHGDHzt3sufcusrkcGzcNMzkxxvzsNJX5KRAOUaww2uC6Dp7n0tvTgxIef/Xle/nsN+8nkB7ZriJBVLc9Cdqxf8I442TsHsLKhX8hqNT9pyXgnUbYs6gIZ7JIwdS+ZZgeFYQ0lhoo0mx7/X8AoDV/Alrz4Blr1Y3ECJEkwXqZKukko7s5SU6xEgNOiJNMRe3/0/YC7Z0BA1u9GdJuSKBS4EQcDws8USlxTbCIcdIoJD1dGdyD/0Q40I8evJSjx06wtFBhaP0GXrBtNw88cB+l3kG6yr2Uu8oEgQ8qtpCLSLrK3fR0l1mzdg1GSLZcsoF33PYKpmbm2PPoIR4dmeLxo9NUfB9lNF5KEscNC93oYj0DCfeAkTSD+LTX+2ImxH5eKcDZ3HE4cxQ3kcrQDwj9iIFrX0Whpw+AQs6gZYQOY2TKW06FTEI5ZCtCCQ5ookoCbUMqo5c7y2I5GTz5fRkrNHmOUEv6nCVKMmA2dpEiRKsC++slCGYhFRFUa1z6wl2IqMa9X/gEPbe+D3fjLiaXlph44CFy2Rylch/HDj9BPudxtNrD8cUsxUzEW27Zxctv3I1ImGfap1DIs3Gj/fm2n7oZgG/f9SCf+drdfH3PXpaCEIOHILZ6LXI2GxQOAkEcq/Oy6isV42LNA553s0CeaqCNwsQGFYSoWNOz9coVymPr+9Lo5eXxFTi3tKHNTRvysP38RiUe4yQruzDGojAohUkwfMyKcmysISsiUkKB8RAoMILRZoFmS6PjBsXeMoMbepHp1Qw9PsrhL3yc4nU/Q3H7C8lmJWFrjoFymsGrb+QbD47xp18/gI5S4IQcGZvn5Tdd/RThP9O55aaruOWmq7hrzyP82d/fwdd/dIB6HJ8s67bZZogpFzLnZWxORejr5ADPcU6gVYyOA3QUIT0LlejmSjjZ0vJj4ihAaJUgKKgk/EmYZDgp4CJpqIG0gq91Ql0qTt6nNUbZ1Ukd2z1goxRaGUvsjrDM9UkCbR2FYqpZpFKH3i5F78ZVuCYGLbn61hcSmR8xctfnqTz+A+7e8G7q6TUYv8JEtcnj+5bQbga68iAE908t8Qd/8U/85vtfe9qc6NQhQSklN91wOTdddxmfu/1f+C+fup0TzRqxcBKaJauka/uKp73Oz8eFmIu6DHpqTVo6LrVaCx0GtGlDiX3i6CSbeRT4SdnTCrtoW/d27d8kibBRyxg/tlG0YlBeJyR3WiVoEInQxxbDM1aWSgkDoZHEWoBuv9aY8SDLbEOSyTq4aYmOY+Kmjyditm1fx9U7BjkcFPibh3N88UeL3P5wwH2HfRpGID1tp0wNBCLL//v1h9h/aPRfWeAz1erDKAbp8PY3vpx/+Yvf4hU7V5OKKxAugWqRMQG7h9c9r0qdz1sFON0HE3s9hI0mKlI4nofnAvWZk/fH0QohT4TYKIxWydizxiSjAibhCiZZgrfIEsbW0SPraVRoLX8cxlb4I4WKtcXj1Iq6ThMaJwmthG2I+S5TDQkmRocRKowwzRazx6dpTk8Quxm+nnmNHVn2Qrtp35WDfNk6oTYxNmkmaoY//fz3z1kcaN/uuc4yNdKmTev48p99hA+//noK0Ry0pumVLdav6jtjiPN0c7OOAvwbhkErXW9pw278hk1+vVyOTCFHdd9dy7F5IZ+3yW67/W80wmhEezQAdTLcMTqJ85P7VDIJGkfoMLBfUUTsh6ggSJQgWaCPNRjFksrja5dlcg2hCWKP8UYKfB/th8T1JvMTC8yNzdGqLPJnT3bx+EIPOC1LiueloVmB6mTC0i7aawZEbpa7nzzB6InJRAFOH47IFQsuQohlJXBTKT72Gz/P/3z/q1mjFrjtxs2s7u86rxDnXAh9nRzgWU6ChRBk117GrMniVltkshnSxSz+5F7m77ud7K7X06hrenUy5qAdcNrlf51UfkxiXTUC+RRFQWurDHGEiaLEeseoOELFVvDjOCaOFbECpOFEVMbXaYSoJfsBGpTLZCtDUJ2m1qpSa0SE1QZuq8pfjvTw6X1lGDiAKF+BCQOoTUB9zvYeUrkE/8SCcSEdRqYr3PXAAd6+bvUZrfGpierKGX4pJR989xvYtmktmzetRTrO2fGVTnlOKeVZ+QM6CvAshkBeoUxp163U7v9HvIxHrlyg0FOk8sN/4OGHHmUxXeSWnZqugodSMVIknABaYxJOXmFsCdR6htiGRFqBUpgE/c1EESoM7VfUhkuJiSJLMI0x+NrhcDgIuDhSEeuk2yw0/zy5itf3HmZVdhIdSiYWIj67N8WnJ9dAKkY0xjEnYojqEDbBzUMqbZVTJEorBK7rEimXh0fGeTs8hQDvfK5Z2xtIKbn5JdcBlpNZSnHe4c3F3CO4qNGh/9XtWiOkpHvXy6geeYTKxF4LVlUqogYj+o89RrioCNbuglIvJopBiqTGrxDGsjOiLRlSO94X2pLdGRUvW34ThcRRRBzaL+sFrCfwI8ibFpNRkf3BEBAnpdRkrFr63Dffwy8++AJe2D3FTGWJew7U2BdugS6JFNruqlQnLDivm7GlStMe1MMqpJRgXLSTYnS2RqvRIJvPo7VByvOrza/0Bm2IxFOF//l8nl/j0MLO7rteinWveD+h18vYvkM0K0vkSnk2blnFQCbGn5lOMHkiC2+uAjskpiKIE2sfR6BCTGyFXYc+OghQfoD2AyI/JPYTBYgsIUUcWzTnUAmKosWjwRoeD/pBNNFanqwqKR9Ek+/PdPM/ntjIXxxez75mH6KURRKgjWuh1dMFSGWW+w42DIsg9hMWetBRgIrhyPg8Y5Ozy424dox/rmu4spImpVjOFZ7O59FJgp8ly392d5sMpJV6Ede8g1G/m8MP76M+t0jak2jhcs+TiywutXCNj45amCiwcb2y1R0T+pjIR0fJ98AnbvlErZPfo2aL2A+Jw4gwiInC2OKCRpqUDmgZjzub2yDO4JjAGm9lknVKH1QTqAEtKPXC6ksx0kEbncwixZamtZ2YG0upRNSyitG3FQqr7Gh2qsTRJc34XHW533CukORc1aILMTydadB/B+HPqfdXFxfxU10Uf/IDPP6lT3L8+48y1FvkSFVyZ1hi50yTrjUOOpJIx7GmwNiuLgmglNHadnijAB0qy7wS2a84TCx+GBGFEWFkrX+kDF2ixUi0im/Wt4Fp2tKl0tarhC2ImlaQVRMcBwp99rZgxlp2E9rHIxJLL4HAYogWV0N5nb090w2ZEkiH0D9BtRWdsRHWjvXPNOJ8vtOez6cm2EWrAGerebc/iMXKIsKvsmbTVio/+S6e+PYXGK3XcNZshJrDIyMVdvXnkNpBxTpBTFEY1e7wanQU2Q5vGzk6ji0SdKRs0hvFhEFb+DVhrPF0RNqN+erSZSz53Qgxi1HC7uDGPsRNiBoQ1RE6wqS7wStCawxpgkTgI7SJrRKIZCo13Q35XsiUIahbpZAuxk0lOw2SIBbLHuB0VaBTh9qeKct9JuDhjgL8G8f/p174p3gHYWg26xRaVXZt38b6Db9Os+kTNBs4+x7lrgMtbpxSXNIfEYWO7RMYnTTEbC9Ax9oiLkQxWhlUFNnEN1bEkSaONFGkiWJNFCtUbOh1mzwZrOazlevANGxzTRlr/aMmhHUIlxBxA+NkobCKwZJk90AGqTYhnRSVWoOR0QlaOiBAEHllTLZsQ6H6hM0PnBw42SSQTaNcj1Cb5RzgQq/f2YT6XI/t9AGeg5r/6S76yp97+/o4sG8fk1NTrF69mqG+XsIoZnJ8gp58jkYs+OtHXP77zXWE38JXAiep9bfDH5SxZHNRjFYKHWsb9kRW2JXSREoRK00UG9JEOMT8ydxLaEYloGKtt4psuONXwV+AcBGDA4Uh1q1fy5c+dAPbB1O0QoWQgjBSzMzOc+zEJD98fIRv3D/C4YkFWsolcrNgUggdYnSCMCdSGCFQSv+rys7ZruOFMMKcydueLzp3RwH+DUKhdsViaXGRWMUU8nnSmSzGGPKFIpsv2cKjjz5Ko14nm8sTRRFLiwsEUcSmgRzfeHSOS/Ie795WYbHqIIxCGhIWyYQDQCuUSsYbYp0Q1yXCH8VEcUwUGYyCXq/KX81dx3eXrgVZsyMTStlaflCB5gyEC3b6tLgOt7SaP/+/ruaqLXb0ILvi/Q0O9HLZjmF++tYX8zGl+NFDT/A3X72bbzxwhIl6i5AUrhfaPDmdQhiD6zgXVLe/0BDoVAF/PswHXbQKYDHwFYcPHGBsbAI/8NEqZMfOnWzeMgzAJcPDxCrm3j17MIAjHbSASBv8VgOvfpw/uHs9a7N5XtY9zbF6ClesxAux485aaeLYoJVK2NetAsSxvT1UknXuHN9c3MLvzb4GTIBQTdtAC+rgL0IwB/6M3TjIr8MpDvHH776GV15hu7dnS1CF43DdNbu57prd7N03wl9/+S7+/ruPMNP00QZkOo9jAjyhl+tgZ+ZSPr+E9nThz7ms+8UYAjkf/ehHP3oxJsAAB/cdYGp6ht6+fnL5EvV6g4ceup/u3m56enqpV6scP34MIxzKXT3kcjnAUKvXCGPNhsEy6Yzk80eKDLkNdhcrVJoWQtCoJMyJdRLuGAtU2xZ+rQljg1GGNe4C317axK/OvJNIZxFqHqOSmL85C7Vxa/3RUFiH03sJH3vnDfz667Ynwn/+wjXQ38PLX3QFN+3cyNSJUSamJgjimLyq8fabt3PJxiF00sk9Xdn43yJMWUlY0lGAZykJrtVqHHhyP+WuHnp6ukilPBw3w9z8PA88cj/rhtYyeuw4SsP6DRtZNTBIububUqlMb1cvveVusrk8XemYOGry90d7KQjNteU50DFBLIiUscltEufHyhAloZCKNXl8ep0aX1rYxm9MvoVAZ3H0AjrwIaxCYxLqYxDMg0zh9FwKhXW8+5bN/OF7rgcsjLm8AMFpd3nXrO7nNTdfhahXGHn8MS7f0MMH3/Eq8rks8FTBP503uFAcn/N9bCcHeJYqQLVqFaTE8zw7GZnQFWEMi5VFjhw5xtCateTzWVzpWB4vY5DStRwV2k5nxiLL8GCA6/j8yYmt3DVT4p1Dh9mSr6OMpBVLYmQyfqBxtcYVmqzjMxWm+OPxl/KZ+ZeA1jhmHBVH4M+DP2O/qwhy/VAahnwPpjGJquZQcYTjemcU/jOVLNuWXWlNrlDgP3/ovfzUS6+nq7tMf1/Pcm50uoLB021enW++0KkCPYvH8zwc6eB6LlK2Z9UipBT8xLU/wcaNm8ikPaR0Eph+SzTRbDbxWy3iKESrGE8KAifPhh5DMVXjRzOr+NWjG9lhDnJz9wSXdTXxpF4G2tW4jDVTfHd+K5+f3cFEaw0wi1CzqLAOfgXCBdu5TRWhayvk1oKJ0LWjELb42p6IRw5PcdWl686rCnM64XOSCUwpJVdcufM0nkKflS/tQpTgTD2FThXoOcoDhBCUu8q4rsRvtcjn2zusgsGBVWzbtp18NotISCOMVoRRTBAmNKJRSBSHKGWH1KQw+CZFMat5626XF73qnXz5zhk+/LXvUho5zrCcpscN0LicCAo8Vu+lFZaBOuj7kKpm53tUaAUi3QW51cjiGlKeJKgcxYR1TCoPXpaKH/P4yCRXXbrutDP1pwrVGTkPzlHqPN9qztP1xJ0q0HN4Uqk0fX09zMwukC8UcD2HbCbN6qE1FAq5ZXwHrW3tPgxD/CDEDwKCICAKA5ROAG8NeFKSzpS5bOeVXLV1NVdtXc2rbhriU19+iC/c8QCthRmb1JoQ5IT9ShDWtPTAK0C2iMj1YLwS6BjdnODV169m36GAJyeM7fJKB4XiXx48zFtuuYJ0yjuntZdSnlcd/nwqOadTgvOllu2Q5P07O+s2bGByYpL5+QUGBwbI5bKYZJRZSIFWOilXKoIwpNVs0Wq2rAJEMdoYHC9FPpMhJR16y11cd92Ny89/w3A/N3z4FczW6tyxx0CrYkOcqImbGyDO9CPSOXLZDFcPr2Gy4nPoxAK05pGtCXTU5IW7buLq4SF+6//7BtpEQAqM5Fs/PMDUXJUNQ72nTVBXCubTieFPF7asXGB5Os9xtvGT9vRpBx36WSiDtr/n8nk2bd5ErbrI3PwcQkpc10FphdaGONZEcYwfhgRBQBj6RGFAEPgYIcmUyoiUiwkDcuksN974IgoFCwteW1pifGwC/CrXrs+DAgZ3Qt8OwGV4/SBf+P338IP/9X6+/Ydv5+u/+wbe/BPrkeGinfURLk6qSDqd4tfeegvrutJ2DsiEYGKWmgH3PLx/WSBXVmVO7dKebebpQkOX8+kCn+nan4srrOMBnoUq0KmrfRs2byYIQ0YOj6BiTXdPN9KJkBKiSNEKAprNFr7vE0cRYRQiXQcvnebJe+/h+N13k2r5FDNZRv/2b9nywhu45QMfwA9jxkfHmBipUqCKWxwiLq6DVBmWRqlVZnnl7j5y2ZP923Qmi3HS4GUwUQ3PhXKhQCpf5Jdveykf+fOv4JsC5LoIwyU+9897eOurX0h7hPtMIcvp6vjnoiW60MrN8xX14XnlAc7kEYYvvZTh4a1U5meYnJhganqWhYUlqrU69VqDRr1Bq9UkCENUHOMVijzxrW9y+DOfZcPYGLtrVdYdP05lzx6yXWXcfJHqUpU49tm6fSe7d12GU+xFxL7dwsr0MlPXPLz3aPI67Os5Ml21SGsqwsQhnoCBboux8x/e9kpu2NyHyBZhw9WYdZdz79EaDz2897QW/Uw7ueeziH4h8fozwe3VWYh5litAp5tHuWR4mGuvvwajQ2YmRjl+7ChjYyeYn5tlaalCrValtlQhXSoxe+wYM9/6FlcWslzb18XWQg63XuVF738vt/7KrxE2mzgoduy6HI1HIZyjp+zYvXk3BV6aSGT40ch8IgAwNb/EI/uOYeIAB7tauWVdD5tXl23ZNpvld/7jWyk62g7FFQap9+/gb+48/LRKkT/uY882JvF0QqqLNQmWF6vVP7XmDdA3MMgNN97ICy7dSqmYIQobVOanWFqco1VfpLurxPoNG1nYs4eNccil3UWKApYmZ0lv284N//FXkLEhatQYXD2EVoa9e/fyjTu+QVQdg2zGLrBkutFS8v39C8uv4Yv3HuWRo/MQ+choiZTUXLNtM1s2nkRqePGNV/ELt+7AqxxBhi2Ul+erT9aYmpp9WtWVs93+bAjj08klOjnAM6QAKy3XSiuUzeUY3raDTVu20qjXWZifp+X7SAzDO3dxaM8e5MMPsaWrSMlojK+Zr9bYftttDG3YwNLcAsVyF0Y6LEyN879vv5PPfekuuPRmGN4BfhOyXbAgeGTfMQB+eHCO3/77x9DZfqRTIJocpyQCbr5yG2BRFhC2efWf3vZSvvfIX3J/bRpKQ5xoCD7z3Sf58FtffMEW/emsNZ5JiH9chblYkSEu6oWY0/EDtL2B56Xo6u6hq7vnKf9/3+1fYLU09EkHJ1ZUFpdIbdzAlpffgsCOGsRC4hBzx12P8rl/fhDWXg2Dl9nleTSkc+DlmZud4mc/fid37Z1isaVxpECrJk4qw1WbB3nNSy9fDpGEEESxoru3mz943y289ve+xmK6QJzp5faHpnjfrYt093ShlDqvxfRnYrvrbFOeFzoufbGuRMrng/CfasnOVOo7/sD9TH7zG/QZRUopiAyVepPuG65j7TXX0mq0EK5LynUYm5jl81/5DggPb/haayv8hDzOdRGFfpq1Op//3pNMVloIHaOXJjCVEXIph/e84RbyhTxKqWXFdB17uV98w25+5w1Xkl06Bsbw8Izgz+943L4PxAVZ3KcThpxNaS5Eoc6VuHcU4N9ZzjDynW+TCwPyAoQ2BEFEQzr0Xn45KSBoNRFC4El44vA4D06H0LuOaPwwcnY/MqqB64HQmHw/uBmMv2iXXhpTmNoIXibNq19yPW97w08C4DgOUkocxwFzEq7kV37uldx2eQ9e9Sihm+PPvnOEJw8eW1aSla/9QkKdM1WPzhWunGuL7ExG52L3As97BWifxYlxTnzz6xQANyHD8IMQVchT2ryJAIiVJc1TRrAwN0VaLdLVnSYjIvTcDProIYgVwnUgnYdsj93x9WcxU09CU3Dr5Vfyl7/5BkDTrNepzFeYnZ6mUasipFyGEQT4X7/6Ol6yWuA2phnTBf7oq0+sCD9OCtSFYPW0BfnUPsG5rPVKT3K+SffzgTFGXqzW/ELd/cy+J4nn58hAAntiCGKFzmRwyt00/Mju+mqNk8qQ8wxrgyfZLR/nls1NXvOiVVy7NY8XNyxBtiuhOGiRHpQi3b2B97zx5fz5f7oVD8XIoWNMTEwxMTnFgf2HufeePYyNHn+KQBfKZf7iw6/jZatCvGCJzz44z99986HkMedePTxb7H5qfvBMlFHP5AGebijWUYBnOC84m1U6uudeXGOQWqNjg1IQI4iEIFAaP4iIlUIrhec4DK1bT/faSyiXCwz3a165Lc/H3vwC3nFVEVrJMnqp1w64NVt85Bdu4y9+65V4HoxOLyFcDyeVplAssWrtOtL5Mg/e/wCTJ0aTypDNDdavW81nf+tNvH6TxI0b/OJf3cvI6OQ5LeyZEtjT5ULP1HU9m8J1iLKfA2E/n7gU7PLLxIP3I41GGFBaECJQjkMrDKkuLBCrmCAIkmX3iG3Dw1xx+S5ypTJ9A5sg1UOpUOLNV/Qx1OdiIo2Ty+H0rIHFKaanZ5ieqXLoyDjTMzNMTkxy5PAIB/YfYGFult6+frx0gSeffJIwCHAci9MfK03/QB+f/c+38aGXbkDGIbf9539kbq5yVsE7W35wrjGJp1tufqa8ckcB/o0U4kxJ2dz4GP7kBGkhkEKiJcQSRDpN0PKZOnqUWBlqtRZhrGi1Ggz2D/Cm176eS4ZWU/Bcipk0VeVy8651vP2F60EZhOuiu9ZAzuXTX/kBt3/3MZoL4xw/eoRjRw8zOnqEgwef4PixoxZqRcBCpUK9Vl1+jU6y4eVlc/y3D7yav//QrThhi9/71FeWH3M2nM+nK5jnqxhPN9G+WM5FPQ59vh/E5KOPgN/C0wYjBMaRFmU8m8HUlhjfv496vU6oNIHvk0ml8IMmuy/bDRpGDh9isL+LHdsvRXoptudmyJUETS0gW0T2raI5NsoX7j3Ez+6CZhiwWG+xtFRhzapBNm+5BKUU9XodKU6f1Lat9ctv2M5129fx8N6jtHyfbCZzwaHK+Vj+H8dyn7qf0CHJew4s/4V8gLXjx1hGOxEW9ycWAuVIspksE488wsSxo/R29zI7O0cul0NpRb3ZwktluWznTnZsv5Sleos/+8zfMXrwITbnXswTagsEoEtrQT7Jd584Rk5B0T9GttjHjddez003vphUOs+BAwfxgxYoy+zedZr3srztVi7ykhfuWr7tQhZizrTAcrrHns9CzIUoTCcHSn7I8QAAFY5JREFUeA4s/5kSs5WnMjWVYOXYyU0FaCFQGEpdZZaOH+eJu75HppBnYnycQ4dGmJ1fYnxskqmxY2zZshmky5e++iW+f98PGJuZZ83CD0G1wI+h3oJKBUaeYLzi8NKbbuHXPvhLvPW2t1MudXP82DEWK7N2S8wYxifGn2JJV1ZQ2j9bdskL7wOc6lFOTY5Pt255tuc4XS7xfMoH3ItV8M/3Nq01czPTSIPlDo41sbDVHy0MQkoGSmW+95lPM7R9Oxs3b+HA3ofRh/ZTWVrEdSFXKCYMkCGR32Kp3kI3f0R3sZtFZxPrCzVe8qaXccsNO7n++mvYtG6AIIg4MTbO+Pg4lcoiGOjuHSCOIvbvO8CqwVWsXb/hrFinP64XPNdG14V40vPZDrsYPYAwF5naXuh8SrVe59M//07Se/fSFylEoAiFoOVKfCGpO5Kq53Fodpa5cpnX/vKvMLR5I2MnRqksLRL5Td7w6tewa9duFhYW2H/oIMdHx9BxQLncx7qNl7JlXS+lchmEwG80GJueZXZ6loVKBd+3XGW9vX2UimUajSbHjh6iVatwyytuZfWated8X0+3nHkhaG7PhBh0FOA5UICzfchCCObnZvn0u99F7tBBekKF1BBJh5YjaShFxW8xG/i0lGJiaYkxYO0113LJlVeSyecZP3yIgoTf+ZNPUu7tJ44ipHRBSKTyUXGIHykaTZ9qrUG93qDVbBCrCCEkhUKB/v4+yuUSruNSrTU5dHA/x44eZP26tbzqp37mvKs2P05p89lIUDvToM+C8J8vNWf7sY1qlVYYkokNsYK43qC6tEgNiIt53IF+1m3agOjqYjibIfADjkxN8cTXv8bcwiInGnXSwC/85kco9vQxPzeLH0T4QUgURhZaRWsUYJSdCC2VS+TzOXK5PLlchkw6heO4FqKFJtVajYbf4kJMz4+7dPJMIEs8l8rVUYAzVDnOBPfR/kB836ceRMhmCzU1Q7avh7WveRW9119HzwsupbRmiFxvH5lyGZFKobShWaszMTrGoYMHefCHe5g5cZyevj48R5LP50inDZkwQillibQROMlCvuc6eKkUnuvguk4y/+OAkFRrDY4eGWFs/Bh9a9Zx+Y03/VgVrx8nhj9XbP9/wij0RV0GPVcFqK0IzUaTidkZSHtc8Wu/zO63v42+4a2kcoWn/D8FaGVpjhwtKK7dwOZiN6JUYuPqftau34Dvt8hlswjpnCSkliBMu8Ka1FlJvglbaAvCmMXFKgcO7mfk6AHKa9fC4cPMf+cbrLntrecUpHONRJzLSFyI8pwpzDrfpLvTB3gWQ6Hz4awKWk0Kw1u47i3v5OrXv5Fu17Kf+kFoeXY9D0fYerB0HHAcRBRjtMJvNKnXamQ2r8N1HYKWT2zMMiyjkNJie0pAWxS6WGviKCIKQ1otn1qtzlK1ykJ1kaVGjXJ3H8fv3sPoF7/AtCcwi4vsfv8HnyI4WlsYR6Ui4ihe9jRG22aGsBs2SCnxUmlS6TSpVPop711r3WbLu2DPcSbYw3MpUicHeBYs/7lq46cqSamvl2te/0a6Nw5Tm5gi29dFIZeBdMqGSNVFatUqke+j4pgQqAeKpVqTSqVCbWGOu/bcS9/wdgYH+qkvVqk3WolQYgXSsQKnYkWsNb7v0wpDmn5Ay28RRwFxGBAeOcoT3/oORx5+CLdQYNrJ8tgn/5T+vEfrppczceggtYVZZqYnmZmapLa4SL1epdlsWA7iKEJrbZGwHYd0NkNP/yB9A6sYWrOerr5+BofWsXb9Jkpd3ectuM9ECPRMrVZ2qkBPI449U0dUSsnx48e46wd3sXpoM5ffcANdWYfphx9k7NFHmTmwn5mDB6nMzlGp11kIY+p+RNyqkevvp7R+PQOXXUZ3XGN1dZaB172LgatfSEkKlDGEUUyYWGilFFEUEoUhfrNBY3GR2sI8tckJ5g+PMPX4I8w+sReQON1ljOPQJwSXo9hcdPjC4GoeqNVYJQQhFtQLktENmz0jhECKxKgLiePY32XiDRACAfT09XPra3+Wn77tbWcMic6GO3SmkOZcoVHHAzzHinG6Dyid8iiUuuhevYpDd3+PJ//2s4zfcy/zYxPUo5gglUJ5HmnPpRy32L52kBf82v/N4JZLyWdS9G3aTD8hE5/8GD/6+ddz9/BV1Lduh0yOlOvgICyvWBSiw5DY9wmWqjQrFepzszTn5ogaLTLpDL1dPQymXQaEglixpCWjRpBfjLgunuCJdIYTsSEnLM1dO9Rp5xRCCJxEyIVQREpYLjMsHKSU9v5ipEln0qfNE87085lyqPPNNy7Wc1F7gPOZZak3anz/3nt46J+/xd5//DxmZpp8qZtyTzdlTzLgaPqlos/VdKuQrAO9b3wr/b/1+7QnRdqpbf2Ln+GRX3w3B5Y0x8tdnAgMNSPsHq82CAzSgIsh47nkMxm681m6syn6pGZQKEoY3MhQCQ0jRjIhJLlIc1PacGchzT8al6yO8ZKU2nGkXbZPqE+lAMdJ/mbCY5bJZtm0ZSuXbtvJ8PYdrN+4mTXrNxBHod07EIJ0Ok0mk7V8Ck/z2p9tgb4Nx94JgZ7lEOhs7X0hBCHwq+9/H4988UtscFPs7O1igwoYCJoUVUxeaDxX2BJoyiNs+jSnZshcdTlbPvXXFLdfjl+vI1NpUimP4AffZuIX30VQbRF091MNIpoaQiGItQABnitJS8i5hozQeFqTknbNMYgNLQVBJJjWgsPCYckYtmnFmrTkE5k0owa6MOjE2svkPUkERjw1rxVAKuXR3dtHuVTGkZIw8Gk2GjjSwUl2jD3PI5OxyXImkyGdSZHL5ymVu+nq6aW7t49VQ2vo6xsgXypRLJbxUqnzLkdfrCHQRd8JfmpirEFp5AorN3/8KH/yzrexZnaGXWFArjIHUYx2XEQ6hUo5KGOIGwFuvojM5zBogmOjpLcNc+nffp7C9t2Evo9wPFKew/Q/fYGj738vGTxkKk0by8EAeBLjSAuRKJNqlcCy0iBQyqC1INKwoCVHpcOYgLwyvMQo7sh5fFk6FOyH8xRJ99pj3MJ6hmUqP60R7VfQLvwIEEZY5Un8mCNEQghuwyUpBI4j8Vxnxe8OmVyeoaG1rF2/gcE169h0yVYGh9bQ1z9IoVhcfklxHAN28b+jAM+BAjzF+hs72AZ2uT167AHqD/+IxTu/S3TvfZjpWaJ8AWfrMN7mS8hcspX00GpkuQRKUXvwYeZv/wqm2bQIcAr846Pkrt7Frq/8E5mhDQS+j5PJ4AKHfvejjP72x0gXehDpFCLWiJRE5JxlRRAyIcl2rHgiBDrSxEoQa6hKwQnX4QiSSBuu05ogJfiE6xIJQVbYhRmVQL67rkApC0/qSIlKwiKtNYL2cs0KOiVh/3x7EhbsrrFYblNYRRACHCFwpA1h2sqQdFuSsMvlsiuvYdeVV3PJ1q1csvUF9PT1P6V0eyHL+x0FeIbDIHunYvE7X2fhe3fQ2HMnqhHgz1QwCvpvezNdL30ZxauvJzU4dFr0nQduuZn6nffgDazGKIVE0pg6wcCbX8uuf/gSGohbLbxslrjV4KGffBnNex8g1TNgX4MEkZEITyz/jrHKaYS0uwiRRhk7jVrTgglHclhKasYwjGGLAx+XkjEhyGOF0mBwpCXyMCtq+1apDLGy1t2VAm0SVhxsvmDMKbDry55ALo+HYwyOTAQfYZt7CSWU0e1rbmy1SVi62W07dnLlNddy44tvZtvOXRetEly8CpAIQvvDXfqXrzN3+z9Q+eEeTKxR2hDXmjjpDC/4xCfpeeVPn/E5/co8R/7n/2Dubz9v5/ulLY4ZAcoPCJdm2fl3f83at7yLMIoxQNpzmfjK7Tzx2jeTLpRxSlnrhQyQkUhX2saVBHSiByQeIFnGb2jBlCM46EpmhWCDMlwjNH/tOTygIZ9YZt2OsRMrr4wNZQSCWOkk+LFKIgTJLgFPseBtb6kSSlZbQj1pBiQC6TzVOwhAG3stHSmJE94FRwhSriTlOhTzed701rfz1ne9l0w22ymDPltK0LY0am6GYx//Xabu+AYqiFChQUgXKSCuh2S6+8lecaW1UCq2YZIQhHMzVB99mNk7vsH8975H68ARUsUyMptBhzFaW2stnBSaDEf/6JMMvvaNONkCkR+gPJeBV72a4o03UL3nPtIpS08q0xKMQCtjA/+TnNtWKbUgRhAAgYBICHwpaRpDUwhiA0VjbL6gNbE52dCVUhArEsEEZSzFqgQipYl1eybDhkzGWAEWrBwiTCxfbJ9UrsAeEjE4AjzXIU5QK8AQa4MUernxF2GIjCDUilawyOf+6i8JgoBf+JUP2XzkIvICF+VCTFv4/akJHvng+1jau58w0qgYHCNwjQW4ErkyweQCe259JaUdlyIzWXSrRbywQGtignBqGtNsgfRwSz0ozyWKFRjQyqCT8EGlcsw9sp/x27/Exre9g0AbwjAim0qz+hfey/Q9P4AwRqQdhAZpV84QSQhkklAjMBA5gkgKfAR1IVh0rDJoIMBgHGvFw1jhJWVOKYVNbpHWy2BzAJFgB0Va21jfGGKlMIArJcpYobWTGgZtkn6BsJ7DCFvgdZNkWEqJK2yJNVQaIS0vsVL2NbhS2txDa9uZVpKMK1FAV0/vRdkHuGhHIQDu/38+zokDh9AxGOMgpUFEGs8IJMZWQZwUrWMTLB0YaQcEOFkXhAuOh9NbRDguUazRobbSKiRGanQiuCabxV9qMP71O1j7tnegMBAEhCmP7ptuxt20hfqJKVLdfaA1JMIppLACZEA7At+BAEEsBL6wSXBFCJrGPkYKgXBhwVi0RSHsc5h2iUknK4rL8asg1idnhFgR8sTa8qNJKZc5kjEgHUGsYmKlcaWd5AuVwTUurmOItMaPkhmn5O/KJEjSKIQRpByHQi7P2vXr2bljO298089y2eUJEPBFlgNc1J3gmXqDEeFgPEFKGTJakxYOKW1wE4voCInTVUZSQiRxsUqSOaQg1Aa0xiTCZiVeYKSDUtqGMikXX6aYHTlOtV4DHOKmj3I9vNWryLz4RUx++jPkHRdcW6bRSQ5hXElsbKgTSUFsDAHQEoIqMC8EdWVQQL8UBJ7hYD22K5xJRUZrbV1EkhAbBJHSGDRKmWX0aeslrLVuZw1ar5jrEQKV5AxiucFncwylFGvWrKWUL7K4uIjRlkI2k85QLhbo6+2hq6uLnp5eNm/exObNl3D5lVfS1d19UVeCLmp49C1v+zm+fM+95KTC0wY3FmQMZA14WCVIGfAESG2SuRk7vuy06+PJ2IERAsdIKzBKW/hE6WBSoJH4uRSB32RmbJxM9wDBUhUvjMgP9KO3b2My5VGQAoFEeA5RsnTftuCREURArA2BMIkCCJaURgnBoCMZTsH3tWY61qSNQQkJWqJUkkIra5WlwHohnQiw1iAMjrQl2EgluYFjFQDEcvBvlUVagDClMUmjTaPJZ3O8653v4NprriaOYxqNBul0mmw2S3d3N+WuLjKngWk5NTTtKMCzkAcIIdhyxZVkrriax++7j6yAvOuQATICUkLgGYNjBGkJrjK4UiC1wUlq5NYSnlQACQhlEuxQGz9rYYiNpJFxKUrB0lKNFimiWhXqDULp0Sj3MFbIkRcG6UiEI8GRqKQZBRBjoVhCIDKGyBiUlGQdSbcxdBnNVyPNnaEig0WoBrFctVFK40gbwsVaI4xASGmbZcbG5e3iQHv7S8XKljhdB2MEWiv7vS2wjiSOYnAcpHQ4cPgwn/rz/83Y6Al+48O/QSp9+k5wG9bxVCDeTg7wLAo/QD6T4SP/5aP8149+lPvu20OkYhwhUUrhSYkDuAJSUpACcsIh7QpSCBwgkzR8bNHblveENtYyGhsqKA2hNtRTDqvKRcZmJwlPHMNvtdDGUJjoYWTsGCeyaQqOwPEcjBA2KXWcJIG1lZRY2g5wZBQxdqI0iGL8SLGkFIvKkBXCJpba4Lou2ujlKpLnWkF3EMtjEu15ISFX1uyT2F1IkCap9YOUjn1PyuY3AoGX8mwFCCvEx8ZO8Def+TTdXSU++Eu/lIx9m+XrJNqTp6f5XDrj0M9yE2zlxb79i1/ki7d/kZGRI8zMLywnfkobhHTsaIC0yTGIJD8giZvtmIEDtpmEHS9wpMBzXaIwJooiUoU8PasGQCkiZZGkpZQEjQa1hUUbWghBpA0KK8AGiJVatuQmqa0bYZNtra2VNoZEYSXCOVma1Fon1ZuTuEZxrJNQaMUQWpJwx1pbT9bujEuBilWCNi1WlJHbAm3DIWM0jhCkPZedL3gBf/yJT7B5y5YzNh/PFZ52FOA5UAKAB370I35w150cPjzCkSNHOTE2Zps5iRmMjRVIG/xKwti6c9c5GRrQTgy1BdNtC4uOY/xGA9dxklEE+zzSS+Fl0svNJyEEOpnLwdjpzUT0krq9LTsKA8ooIqVxpYMQScVGKxwpkyZyMgTX7uK6DlqtQIA2BpUIdLsL3MYbNdpYZTYGx3GWxybaow5SClwhSTkOuVyWrVuHufrKK3nnz72LwVWDFyT8F6sSXNQeYKXLPd2FP7h/P6Ojo4yNneDIyAhzc7OcODHG+MQ4JkFm1oblejeAMgZHYhEklF5eQLGFmBWunpOzM+YpVZiT8zZ2Psn2VF1pQwfHdVg1uJr169ayevUQjz32GAcPH0Illt6WL81y3tqu6WuTvNbk72tjY3iRLM+cvA72H9H2GknPoL0sI5M1zrVr17BuzVrWrlvLlku2sHnzZi7bvZtiMuzWBuQ9H4G+mOHRnxcbYae7/3Rxaq1aZW52lsXFRarVJaanpzkycpjpySkqlUWarQaNRoNWs0kUR7SC4GRn0xirEMm4gVI6+TvCegTXJeWlSKVSFAp5uspdFIslevp6GOgfZGhoDX39fZRKZYqlEr29vZS7uhgfG+PAvn12Yf7wYaanp1laXKJWr9H0fSI/AGkVU+tk8T7pG9gOrzwZ1ghBJpMlnUnTVS5TLBYpd3XR19fH6lWrGRoaoquri0KxSE93D7199jWsPO2Q6lw9mE4I9O8gCT6bYpyKa9ke2T1dRaPVbNp1xihGxTH1ep2W36LZaBLHIXGsiKKIMIoQQDqVAiHscrzrkE7ZOft0Jk0+nyeVSuN6Ll6iEOl0+rzeX7PZwG/5RJFdqK9Wq1SrS/i+TxxGtAIf3w8S5bYsN4VcDi/l4XoehUKBcrmLYqFAKp3GdV1SqRSpTIbUWRZhVBwvb6CdqZR5PggTHQXonM65yI7sXILO6ShA53RORwE6p3M6CtA5ndNRgM7pnI4CdE7ndBSgczqnowCd0zkdBeiczukoQOd0TkcBOqdzOgrQOZ3TUYDO6ZyOAnRO51zE5/8Hsw9g8YITMegAAAAASUVORK5CYII="/>
		<style>
			*{outline:none;font-family:Arial;cursor:default}
			html,body{min-height:100%;margin:0;padding:0;background-color:black;-webkit-user-select:none;-moz-user-select:none;user-select:none;-webkit-tap-highlight-color:transparent;touch-action:none}

			.endlessrunner-container{position:fixed;left:0;top:0;right:0;bottom:0;width:100%;height:100%;display:none}
			.endlessrunner-canvas{position:fixed;left:0;top:0;right:0;bottom:0}

			.endlessrunner-soundhandler-container{position:fixed;right:10px;top:10px;width:58px;height:58px;z-index:998;padding:5px;border-radius:58px;display:none;background:linear-gradient(to bottom, #5cbbee, #0046a9);cursor:pointer}
			.endlessrunner-soundhandler-wrapper{background:linear-gradient(to bottom, #3681D7, #2467C3);border-radius:58px;cursor:pointer}
			.endlessrunner-soundhandler-off{width:58px;height:58px;background-image:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADoAAAA6CAYAAADhu0ooAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH5AoeEzoScwd5IQAAAkNJREFUaN7tmctqFEEUhr/TXra+hKAJrsWgG925cKkYIQuJIaDxRkTiJeow8YaoMSGIUYSsBnThMu7UF8jCp1FG+d0MGMZqUp3q6fS051t2n5ruf/5Tp05Vg+M4juM4juOUi6RM0gVJzyUdbqpIk/Ref/kp6VATha7oX2Z34l2yAYp8BlwM3NrTGKGSWsCNOmVXNgCRN4F7dZtGWckiZ4CndawXWYkiJ4GluhbGrCSR54BVwOoqdHekkDHgMnAQ2BsIOZD6p0kaB44DX8ysEznmPDAGrJvZp1S3piX9VnnMBZ4x0RfTjniv5b4xp1JE7pfUVbmEhK4F4toFRErSq5Q5ejY2vRP5Frh2NyRW0jIwE/kb0Y6+UfnM5TzrZU58ewsno1J9K7d2VVUVzey6JIBrAWcN2Jfj5IKZzacWondVORrh7LacHHhTn+IssBgRWsjJ2gmNFFs4XWspdKj2o4m1YTFQlPoL1MJQC+0t/FcjQu9IejiUQiUtAVcCt1o5c/a2pEdlPHi1woYhrxl4ELH0PE4VOl+FUElTObGtAuvs6ZTU7QDdCrL2WOBa28zuF1h6jqa6OlXBNm18G9u0fmdPJm28zeytpO/AJWA0Z8xoyjGmmXUkdYETwFcz+xDZG28AR4DPZrZeRbU8I+lXGb1urRuGngOTgBrfGZnZWi+9m98CmtlrYLbxQntiX9D0k/pNYtvAk/9i92Jmtwif3HdpGr0PwZv75R+SRnZkM1+FWGACGAE+mtkGjuM4juM4jjN4/gAHvpaZHSK93AAAAABJRU5ErkJggg==");background-repeat:no-repeat;cursor:pointer}
			.endlessrunner-soundhandler-on{width:58px;height:58px;background-image:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADoAAAA6CAYAAADhu0ooAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH5AoeFAAole1obwAAAuJJREFUaN7tmV1oj1Ecxz+/MaU2KTIXSBLb4gKlvFwok+KOGq3QvC1vNyxxQe1CLiQSUyPahShJSsqFG2tKRNSSt6upzcXyVkObfd2c5e84m//2f/5v63xunvOc7znneb7POf/feflDJBKJRCKRSCSSLJJKJO2UdFrS0rFq0iRd0R9+SlowFo1e0L8cyse7lGTR5Clgb0AqHTNGJTUBjQm0M0tSjaSyQhyuhzU8R9Jsp1ZSn6vTKWluIZncr/+TrtEOr957SVPzPnQl7QDOJfjdfnj3c4Brkkry2ZN1kn4pPdLt0dVuOvLZM5p3HJ/mQ5cBB4BKYEKgyPxMR4eklcAa4Clw18weSKoHrnlFT0i6YWafku6thhH01qh6VNJaSQMpZZpStJZAG8eTNjk3JfJl0+jVQLkNTpssqcfTuiSVJhmMNqc7vDPkTSCvWVK5mX0GznjadGBVkkZn5iienQXavLyKlJXVZUCevi5Jo+Ny4dLMvgObgK+etN3p3cBjT1teEGvdUZjtAi562fMkzXbpR55WLcmKzqjjTiBvobu+9fLLgEnFajQUlCrctSegFa3R0PsMBiEbRis6o5WBvG53DS3ovxSr0Y2BvBcpy8y/TJrZt6Iz6qLrbi+7w8w+uPQKX0tyehnIkcly4BYw0ZMuO30msMTT2pM02pmjDm0EFgee3eLSDYE695I0eh3oy4HRGYFousvMeiVNc1tE/yO0JWbUzN4B+3IwhFuB/pT7Q2Z236XPBObL82b2K9GNt5ldkvTSGa4eok41GRxjmtlDSYuAGuCJmbUP7oWBOq/4R6A5XxGzVlJ/wkcp64doc2u+p4dt3ilBpkafB+reHslCPivzqJm1uuGdLV4B9WYmCgFJBxMcur2uzmtJsyg0JB3L1KhrZ4qkxSM9H8q12ZOZGs32tiip3+xRwif3fYw13B/BqeeyPyRV5eWoJhdmgS1AFXDTzJ4RiUQikUgkEolkn9+IjKNpTHVMIQAAAABJRU5ErkJggg==");background-repeat:no-repeat;cursor:pointer}

			.endlessrunner-score{position:fixed;left:0px;top:10px;border-bottom-right-radius:15px;border-top-right-radius:15px;font-size:30px;color:white;min-width:120px;text-align:left;z-index:999;display:none;background:linear-gradient(to bottom, #008ae2, #0046a9);padding:3px 0px 3px 0px}
			.endlessrunner-score-wrapper{background-color:#022c5c;border-top-right-radius:15px;border-bottom-right-radius:15px;margin-right:3px;padding:7px 5px 7px 7px}
			.endlessrunner-score-wrapper-ring{position:fixed;width:35px;height:35px;background:url("data:image/png;base64,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");background-repeat:no-repeat}
			.endlessrunner-score-wrapper-value{margin-left:44px}

			.endlessrunner-gameover-background{position:fixed;left:0;top:0;right:0;bottom:0;text-align:center;background-color:rgba(0,0,0,0.875);display:none}
			.endlessrunner-gameover-container{position:relative;top:50%;transform:translateY(-50%);text-align:center;color:white}

			#endlessrunner-gameover-label{display:inline-block;width:410px;height:70px;background-image:url("data:image/png;base64,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");background-repeat:no-repeat;background-size:cover;margin-bottom:45px}

			.endlessrunner-gameover-container1{width:auto;display:inline-block;text-align:left;margin-bottom:55px}
			#endlessrunner-gameover-high{color:#ffffff;font-size:30px;opacity:0;text-align:right;width:130px;display:inline-block;vertical-align:top;padding-top:18px}
			#endlessrunner-gameover-high-value{color:#FF8400;font-size:60px;font-weight:bold;opacity:0;text-align:left;display:inline-block;margin-left:15px}
			#endlessrunner-gameover-score{color:#ffffff;font-size:30px;opacity:0;text-align:right;width:130px;display:inline-block;vertical-align:top;padding-top:18px}
			#endlessrunner-gameover-score-value{color:#FF8400;font-size:60px;font-weight:bold;opacity:0;text-align:left;display:inline-block;margin-left:15px}

			.endlessrunner-gameover-container2{width:auto;height:110px;display:inline-block}
			#endlessrunner-gameover-facebook-container{display:inline-block;text-align:center;width:68px;height:68px;padding:5px;border-radius:68px;background:linear-gradient(to bottom, #ad84d6, #442167);cursor:pointer;margin-right:30px;display:inline-block;vertical-align:middle}
			#endlessrunner-gameover-facebook-wrapper{background:linear-gradient(to bottom, #7f3fbe, #67339a);border-radius:68px;cursor:pointer}
			#endlessrunner-gameover-facebook-button{width:68px;height:68px;background-image:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEQAAABECAYAAAA4E5OyAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH5AofEhAOsRPkFAAAAoRJREFUeNrtmk1rE1EUhp8zjYmh2JV06UIRdFHcu1IQ9C/4F8SFK0EQ/AO60IVbFy4F/4ULF+4UBS0oVbFKbavGRpvkddE79BJNZkIzmZl4Hhjm42bmzrxz7vm4GXAcx3Ecx3Ecx3Fqj5XVsaTEzAZh+zhwCbgIrADLwGHgO/AV2ArLc+Cema3G59ceSRbWC5KuSHqnfHyTdCGc2yji3hpliGFmktQCbgNXJ7zfQq06KUOMsHtrAjHSc3aj7foLkvYXzP5aONavkq9LZmwd6cNfBtpBjIUJLrNU9DBvzNg6+pJWgLNjXogii+iESNMNUWcj7M+PIMBp4NSIoZCK0QHuAg+A98GKktC+A2BmvboLkjrD5Ry+4rqZ3c/pnGvrVAeRHxjX/gR4FOcrf6lWkBgzFSTKKtsZFvTKzL6UlTQmVI9umZ1XURBzQSrE1AUZ5QgjdvIOmVHOM0cf1Qi7UeG2BJwM148f6jdwImOoHJN0BmhGxxSutQmsmtluXcr6Rlifk7QmqStpS9J2tHQzSvxfQ7/flrQZ2h5KWkznU+qUmDVDAtYEWhnp+fDxZlj+xU8z6wyF6Vo41UHkCzRBJLGMpO1tOrE0D5nqQVL+tI756GF338I+Ax+KHC6zEsSmJMgnYK2ugtgYx5jn4WPSCaSNMBVQKNOOMqnzew3cBBaBeN6ix97fDefHRJ6nwGP2J5T7wBHgWRphiqx2i8hFLKP9xoj8oxfWd+Yqdc/x9toZ7a3/rbizvO1F1ixe7bogLogL4oK4IC6IC+KCuCAuiAvighTTZ1KFl1RG56O+LVVG+0xolNDnG+AFcAg4yt73Ij+A9SDKSx+4juM4juM4juM4zoH4A5scYeP8FHErAAAAAElFTkSuQmCC");background-repeat:no-repeat;cursor:pointer}

			#endlessrunner-gameover-restart-container{display:inline-block;text-align:center;width:98px;height:98px;padding:5px;border-radius:98px;background:linear-gradient(to bottom, #5cbbee, #0046a9);cursor:pointer;display:inline-block;vertical-align:middle}
			#endlessrunner-gameover-restart-wrapper{background:linear-gradient(to bottom, #3681D7, #2467C3);border-radius:68px;cursor:pointer}
			#endlessrunner-gameover-restart-button{width:98px;height:98px;background-image:url("data:image/png;base64,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");background-repeat:no-repeat;cursor:pointer}

			#endlessrunner-gameover-twitter-container{display:inline-block;text-align:center;width:68px;height:68px;padding:5px;border-radius:68px;background:linear-gradient(to bottom, #ad84d6, #442167);cursor:pointer;margin-left:30px;display:inline-block;vertical-align:middle}
			#endlessrunner-gameover-twitter-wrapper{background:linear-gradient(to bottom, #7f3fbe, #67339a);border-radius:68px;cursor:pointer}
			#endlessrunner-gameover-twitter-button{width:68px;height:68px;background-image:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEQAAABECAYAAAA4E5OyAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH5AofARop8Jak3AAAA5BJREFUeNrtml2IFlUYx39n91VCW4NQ+jCJqEAoXUskKKOEPq6Cgi7qLhKii6AEb4SMAi+CEPKigkioiOhCCoQgIaOIvkSqm8KMXkmk9ML8WmV1391fN+elw/DuurszszPB+cEwMxfvmTP/+T/neeaZFzKZTCaTyWQymUwmk8lkMpnMf4SyA6ghhGDxuIrx4vlVwBpgOTAOHA4hdGf6TVVzqQQ1lPjtUHK8Wn1TPaKeVM+pp9W/1H3qo9OMsUrdpC5t4uaH4v5hdUNVQqrPqWe8PB+qN6gj6nr1FfUfdUuTjhhRD6i/qddVMN5W58ZB9St1Kp5/r17ZpCB3Rzurfq1eX2KsTeqk8+cbdSSOtXE+cxiqQJNrgP4T2QjsVe+c57qyrcScjgL7gJfU74Brm3LIE/HppE/2uPr0XERRb1MvlXDHafViPN7VZMg8kkxqqjDJvcXFVu2oQ0WB1GdLiJFed3vTafYO9dgAl/S5qO4clIUKmWWH5bigbq4i/VchykdxUhMzTPhsTJPPqKMDxthWUpCfq7iXTkkhOiGEHvAO8HgczwEVsMAI8GTcuuph4A/gd6ALrCx5L8OtqkrV52cR59M5aCym7qkSDvmpcYeEEFQfANYCHwPrgKdmeG+a7npVlNnHqxCkijpkBbATOADcAow1ZNgjjTsk0gXORmFWNBjBv7bFIQeB/fF4okFBfmhcEHU4hDAJ7I6ZZFHcLzRHgV/a4JCpuLh+CryYLJ69BRbmkxDChVa03Aqpd7vNcH+r+pAFUe5TP0jK+bro1yxfVNn/qGJRJemp3gXcDhwCTtQYNmk1vDuEMEYbUUeTJ3ipRnf0K9796hLajPpaMvHJGkPlfH/tUDttFCLE/WJ1T02ipO86O1rxqj9LUZaqr9cYMp+pV7RajESU9NvKg+rb6qFo8Sroqjfxf0IdLpyvUt+oIEyOxUzWXjfEHmlHXTRocVPXqO/FFt9c6aUdsbTT1vpQKTSRb1UfU9+/TFtxts7Yk37vqVOMUPLmNwD3JoXS1bEnMgqsrmB+Z4CXgV0L9eG6bP4+FavdF6bpiTpP0XvAu8BbIYQf+65oxdf8WTplpbpZ/bJEiKj+qb6qrk/DI81cdVM2ZAb9L2Mt8BBwD3AzsAxYAiyOburFbRw4B/wdm0yfA982/RofanZOAG6M4bQshug4cB44CXRDCBNkMplMJpPJZDKZTCaTyWQa418QsHmFfQ0v1AAAAABJRU5ErkJggg==");background-repeat:no-repeat;cursor:pointer}

			.endlessrunner-gameover-fadein{animation:fadeIn 1.5s;animation-fill-mode:forwards}

			.endlessrunner-about-label{position:fixed;left:50%;transform:translateX(-50%);white-space:nowrap;bottom:0;z-index:399;margin-bottom:20px;padding:10px;border-radius:10px;text-shadow: 0 0 3px #000000,0 0 7px #000000;font-size:25px;font-weight:bold;color:white;background-color:rgba(0,0,0,0.5);display:none}
			.endlessrunner-about-hidden{animation:fadeOut 1.5s;animation-fill-mode:forwards}

			@keyframes fadeOut{0%{opacity:1}99%{opacity:0.01}100%{opacity:0}}
			@keyframes fadeIn{0%{opacity:0}100%{opacity:1}}

			@media only screen and (orientation:portrait)
				{
				.endlessrunner-gameover-container1{margin-bottom:90px}
				#endlessrunner-gameover-label{width:340px;height:50px;margin-bottom:100px}
				}

			/* iPods and other very small devices (portrait and landscape)(very small) */
			@media screen and (min-width: 0) and (max-width: 320px)
				{
				.endlessrunner-gameover-container1{margin-bottom:55px}
				#endlessrunner-gameover-label{width:300px;height:50px;margin-bottom:50px}
				#endlessrunner-gameover-high{font-size:27px;padding-bottom:20px}
				#endlessrunner-gameover-score{font-size:27px;padding-bottom:20px}
				#endlessrunner-gameover-facebook-container{margin-right:7px}
				#endlessrunner-gameover-twitter-container{margin-left:7px}
				.endlessrunner-about-label{font-size:18px;margin-bottom:10px}
				}

			.endlessrunner-pleasewait{position:fixed;width:64px;height:64px;left:0;right:0;top:0;bottom:0;margin:auto auto;border:0}
			.endlessrunner-pleasewait-spinner{color:white;display:inline-block;position:relative;width:64px;height:64px}
			.endlessrunner-pleasewait-spinner div{transform-origin:32px 32px;animation:endlessrunner-pleasewait-spinner 1.2s linear infinite}
			.endlessrunner-pleasewait-spinner div:after{content:" ";display:block;position:fixed;top:3px;left:29px;width:5px;height:14px;border-radius:20%;background:white}
			.endlessrunner-pleasewait-spinner div:nth-child(1){transform:rotate(0deg);animation-delay:-1.1s}
			.endlessrunner-pleasewait-spinner div:nth-child(2){transform:rotate(30deg);animation-delay:-1s}
			.endlessrunner-pleasewait-spinner div:nth-child(3){transform:rotate(60deg);animation-delay:-0.9s}
			.endlessrunner-pleasewait-spinner div:nth-child(4){transform:rotate(90deg);animation-delay:-0.8s}
			.endlessrunner-pleasewait-spinner div:nth-child(5){transform:rotate(120deg);animation-delay:-0.7s}
			.endlessrunner-pleasewait-spinner div:nth-child(6){transform:rotate(150deg);animation-delay:-0.6s}
			.endlessrunner-pleasewait-spinner div:nth-child(7){transform:rotate(180deg);animation-delay:-0.5s}
			.endlessrunner-pleasewait-spinner div:nth-child(8){transform:rotate(210deg);animation-delay:-0.4s}
			.endlessrunner-pleasewait-spinner div:nth-child(9){transform:rotate(240deg);animation-delay:-0.3s}
			.endlessrunner-pleasewait-spinner div:nth-child(10){transform:rotate(270deg);animation-delay:-0.2s}
			.endlessrunner-pleasewait-spinner div:nth-child(11){transform:rotate(300deg);animation-delay:-0.1s}
			.endlessrunner-pleasewait-spinner div:nth-child(12){transform:rotate(330deg);animation-delay:0s}
			@keyframes endlessrunner-pleasewait-spinner{0%{opacity:1}100%{opacity:0}}
		</style>
		<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, user-scalable=no"/>
	</head>
	<body>
		<div class="endlessrunner-container"></div>
		<div class="endlessrunner-about-label" id="about"></div>
		<div class="endlessrunner-soundhandler-container">
			<div class="endlessrunner-soundhandler-wrapper">
				<div class="endlessrunner-soundhandler-off" id="endlessrunner-soundhandler"></div>
			</div>
		</div>
		<div class="endlessrunner-score">
			<div class="endlessrunner-score-wrapper">
				<div class="endlessrunner-score-wrapper-ring"></div>
				<div class="endlessrunner-score-wrapper-value"></div>
			</div>
		</div>
		<div class="endlessrunner-gameover-background">
			<div class="endlessrunner-gameover-container">
				<div id="endlessrunner-gameover-label"></div>

				<br />

				<div class="endlessrunner-gameover-container1">
					<div class="endlessrunner-gameover-high" id="endlessrunner-gameover-high"></div>
					<div class="endlessrunner-gameover-high-value" id="endlessrunner-gameover-high-value"></div>

				<br />

					<div class="endlessrunner-gameover-score" id="endlessrunner-gameover-score"></div>
					<div class="endlessrunner-gameover-score-value" id="endlessrunner-gameover-score-value"></div>

				</div>

				<br />

				<div class="endlessrunner-gameover-container2">
					<div id="endlessrunner-gameover-facebook-container">
						<div id="endlessrunner-gameover-facebook-wrapper">
							<div id="endlessrunner-gameover-facebook-button"></div>
						</div>
					</div>

					<div id="endlessrunner-gameover-restart-container">
						<div id="endlessrunner-gameover-restart-wrapper">
							<div id="endlessrunner-gameover-restart-button"></div>
						</div>
					</div>

					<div id="endlessrunner-gameover-twitter-container">
						<div id="endlessrunner-gameover-twitter-wrapper">
							<div id="endlessrunner-gameover-twitter-button"></div>
						</div>
					</div>
				</div>

			</div>
		</div>
		<div class="endlessrunner-pleasewait"><div class="endlessrunner-pleasewait-spinner"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div></div>
		<script src="EndlessRunner.js"></script>
		<script>

			var URL_TO_SHARE = "https://lrusso.github.io/EndlessRunner/EndlessRunner.htm";

			var PLANE_WIDTH = 20;
			var PLANE_LENGTH = 1000;
			var PADDING = PLANE_WIDTH / 5 * 2;
			var OBSTACLES_COUNT;
			var OBSTACLES_POSITION_X = [-(PLANE_WIDTH - PADDING) / 2, 0, (PLANE_WIDTH - PADDING) / 2];
			var OBSTACLES_POSITION_Z = -PLANE_LENGTH / 2;

			var soundEnabled = false;

			var camera;
			var containerWidth;
			var containerHeight;
			var directionalLight;
			var globalRenderID;
			var hemisphereLight;
			var renderer;
			var scene;
			var score;

			var hero;
			var heroActions;
			var heroLoader;

			var grassLeft;
			var grassRight;
			var grassMaterial;
			var grassGeometry;
			var grassTexture;
			var grassLoader;

			var stoneRoad;
			var stoneMaterial;
			var stoneGeometry;
			var stoneTexture;
			var stoneLoader;

			var oceanFloor;
			var oceanMaterial;
			var oceanGeometry;
			var oceanTexture;
			var oceanLoader;

			var cloud1;
			var cloud2;
			var cloud3;
			var cloud4;
			var cloudsLandscapeX1 = -300;
			var cloudsLandscapeX2 = -200;
			var cloudsLandscapeY1 = 50;
			var cloudsLandscapeY2 = 150;
			var cloudGeometry;
			var cloudMaterial;

			var ringGeometry;
			var ringMaterial;

			var enemyGeometry;
			var enemyMaterial;

			var backgroundEnemy;
			var backgroundEnemyHole;
			var backgroundEnemyHoleGeometry;
			var backgroundEnemyHoleMaterial;

			var obstacles = [];
			var obstaclesLastPositionX = null;
			var obstaclesSpawnIntervalID;
			var obstaclesCounterIntervalID;

			var touchstartX = 0;
			var touchendX = 0;

			var movingLeft = false;
			var movingRight = false;
			var movingDestinyX = null;

			var game_running = true;

			var actions = null;
			var clock = new THREE.Clock();

			var cameraDegreesStart = -90;
			var cameraDegreesEnd = 0;
			var cameraPositionXStart = -45;
			var cameraPositionXEnd = 0;
			var cameraPositionZStart = 50;
			var cameraPositionZEnd = 10;
			var cameraIntroTime = 4000;
			var cameraIntroDone = false;
			var cameraInitialTimestamp = null;

			var userLanguage = window.navigator.userLanguage || window.navigator.language;

			var STRING_HIGH = "";
			var STRING_SCORE = "";

			// CHECKING THE USER LANGUAGE
			if (userLanguage.substring(0,2)=="es")
				{
				STRING_HIGH = "RECORD";
				STRING_SCORE = "PUNTAJE";
				}
				else
				{
				STRING_HIGH = "BEST";
				STRING_SCORE = "SCORE";
				}

			function render()
				{
				// CHECKING IF THE GAME IS NOT OVER
				if (document.getElementsByClassName("endlessrunner-gameover-background")[0].style.display!=="block")
					{
					// SETTING A DELAY FOR PREVENTING A FASTER GAME EXPERIENCE USING NEWER DEVICES
					setTimeout(function()
						{
						// SETTING A VARIABLE TO THE REQUEST ANIMATION EVENT
						globalRenderID = requestAnimationFrame(render);
						},2);

					// CHECKING IF THE GAME IS RUNNING
					if (game_running==true)
						{
						// CHECKING IF THE INTRO IS DONE
						if (cameraIntroDone==false)
							{
							// CHECKING IF A INITIAL INTRO TIMESTAMP WAS SET
							if (cameraInitialTimestamp==null)
								{
								// SETTING THE INITIAL INTRO TIMESTAMP
								cameraInitialTimestamp = Date.now()
								}

							// CHECKING IF 4 SECONDS PASSED SINCE THE INTRO STARTED
							if (Date.now()>cameraInitialTimestamp+cameraIntroTime)
								{
								// CHECKING IF IT IS NECCESARY TO ROTATE THE CAMERA
								if (cameraDegreesStart!=cameraDegreesEnd)
									{
									// UPDATING THE CURRENT CAMERA ROTATION VALUE
									cameraDegreesStart = cameraDegreesStart + 2;

									// ROTATING THE CAMERA
									camera.rotation.y = cameraDegreesStart * Math.PI/180;
									}

								// CHECKING IF IT IS NECCESARY TO MOVE THE CAMERA X POSITION
								if (cameraPositionXStart!=cameraPositionXEnd)
									{
									// UPDATING THE CURRENT CAMERA X VALUE
									cameraPositionXStart = cameraPositionXStart + 1;

									// MOVING THE CAMERA
									camera.position.x = camera.position.x + 1;
									}

								// CHECKING IF IS NECESSARY TO MOVE THE CAMERA Z POSITION
								if (cameraPositionZStart!=cameraPositionZEnd)
									{
									// UPDATING THE CURRENT CAMERA Z VALUE
									cameraPositionZStart = cameraPositionZStart - 1;

									// MOVING THE CAMERA
									camera.position.z = PLANE_LENGTH / 2 + PLANE_LENGTH / 25 - cameraPositionZStart;
									}

								// CHECKING IF THERE IS NO NEED TO MOVE THE CAMERA AT ALL
								if (cameraDegreesStart==cameraDegreesEnd && cameraPositionXStart==cameraPositionXEnd && cameraPositionZStart==cameraPositionZEnd)
									{
									// SETTING THAT THE INTRO IS DONE
									cameraIntroDone = true;

									// ADDING AN OBSTACLE
									addObstacle();
									}
								}
							}

						// MAKING THE HERO TO MOVE (IN THIS CASE, USING THE RUNNING ANIMATION)
						heroActions.update(clock.getDelta());

						// MOVING THE GRASS, ROAD AND OCEAN TEXTURES TO GIVE THE ILLUSION OF AN ENDLESS ROAD
						grassTexture.offset.y -= .03;
						stoneTexture.offset.y -= .03;
						oceanTexture.offset.y -= .00012;

						// CHECKING EVERY OBSTACLE
						obstacles.forEach(function (element, index)
							{
							// GETTING AN OBSTACLE
							var obstacle = obstacles[index];

							// CHECKING IF THE OBSTACLE IS COMING TO THE SCREEN
							if (obstacle.position.z < PLANE_LENGTH / 2 + PLANE_LENGTH / 10)
								{
								// PULLING CLOSER THE OBSTACLE
								obstacle.position.z += 6;
								}
								else
								{
								// OTHERWISE, THE OBSTACLE IS BEHIND THE SCREEN (NOT VISIBLE)
								// AND IT WILL BE GOING BACK TO THE END OF THE ROAD ONCE MORE

								// UPDATING THE OBSTACLE POSITION
								obstacle.position.x = getRandomPositionX();
								obstacle.position.z = OBSTACLES_POSITION_Z;

								// MAKING THE OBSTACLE VISIBLE (IF IT IS A RING THAT THE USER GOT, IT WILL BE INVISIBLE AT THIS POINT)
								obstacle.visible = true;
								}

							// CHECKING IF THE OBSTACLE IS A RING
							if (obstacle.name=="ring")
								{
								// ROTATION THE RING
								obstacle.rotation.y = obstacle.rotation.y + 0.12;
								}
							});

						// CHECKING IF THE HERO MUST BE MOVING
						if (movingDestinyX!=null)
							{
							// CHECKING IF THE HERO HASN'T ARRIVE TO THE DESTINATION
							if (hero.position.x != movingDestinyX)
								{
								// CHECKING IF THE HERO MUST BE MOVING TO THE LEFT
								if (movingLeft==true)
									{
									// MOVING THE HERO TO THE LEFT
									hero.position.x = hero.position.x - 0.5;
									}
								// CHECKING IF THE HERO MUST BE MOVING TO THE RIGHT
								else if (movingRight==true)
									{
									// MOVING THE HERO TO THE RIGHT
									hero.position.x = hero.position.x + 0.5;
									}
								}
								else
								{
								// OTHERWISE, THE HERO ARRIVES TO THE DESTINATION
								// AND ALL THE MOVING VARIABLES WILL BE CLEARED
								movingDestinyX = null;
								movingLeft = false;
								movingRight = false;
								}
							}

						// CHECKING IF THERE IS A COLLISION BETWEEN THE HERO AND AN OBSTACLE
						if (detectCollisions() === true)
							{
							// DISPLAYING THE GAME OVER MESSAGE
							gameOver();
							}
						}

					// RENDERING THE SCENE
					renderer.render(scene, camera);
					}
				}

			function getRandomPositionX()
				{
				// FUNCTION FOR GETTING A RANDOM INTEGER
				function getRandomInteger(min, max)
					{
					return Math.floor(Math.random() * (max - min + 1)) + min;
					}

				// GETTING THE LAST OBSTACLE X POSITION
				var newPositionX = obstaclesLastPositionX;

				// LOOPING UNTIL THE NEW OBSTACLE X POSITION WILL BE DIFFERENT FROM THE LAST ONE
				while (newPositionX==obstaclesLastPositionX)
					{
					// GETTING A NEW RANDOM OBSTACLE X POSITION
					newPositionX = OBSTACLES_POSITION_X[getRandomInteger(0, OBSTACLES_POSITION_X.length - 1)];
					}

				// UPDATING THE LAST OBSTACLE X POSITION
				obstaclesLastPositionX = newPositionX;

				// RETURNING THE NEW OBSTACLE X POSITION
				return newPositionX;
				}

			function detectCollisions()
				{
				// LOOPING ALL THE OBSTACLES
				for (var i = 0; i < obstacles.length; i ++)
					{
					// CREATING A BOX ARROUND THE HERO CURRENT POSITION
					var heroMeshBox = new THREE.Box3().setFromObject(hero);

					// CREATING A BOX ARROUND THE OBSTACLE CURRENT POSITION
					var obstacleMeshBox = new THREE.Box3().setFromObject(obstacles[i]);

					// CHECKING IF THERE IS AN INTERSECTION BETWEEN TWO BOXES
					if (heroMeshBox.intersectsBox(obstacleMeshBox)==true)
						{
						// CHECKING IF THE OBSTACLE IS A RING
						if (obstacles[i].name=="ring")
							{
							// UPDATING THE SCORE
							score = score + 1;

							// HIDING THE RING
							obstacles[i].visible = false;

							// UPDATING THE SCORE LABEL
							document.getElementsByClassName("endlessrunner-score-wrapper-value")[0].innerHTML = score;

							// PLAYING THE RING SOUND
							playRing();
							}
							else
							{
							// RETURNING THAT THERE IS A COLLISION BETWEEN THE HERO AND THE OBSTACLE
							return true;
							}
						}
					}

				// RETURNING THAT THERE IS NO COLLISION
				return false;
				}

			function loadGameResources()
				{
				// GETTING THE CONTAINER SIZE
				containerWidth = document.getElementsByClassName("endlessrunner-container")[0].offsetWidth;
				containerHeight = document.getElementsByClassName("endlessrunner-container")[0].offsetHeight;

				// SETTING THE RENDER CONFIGURATION
				renderer = new THREE.WebGLRenderer({alpha:true});
				renderer.domElement.className = "endlessrunner-canvas";
				renderer.setSize(window.innerWidth, window.innerHeight);
				renderer.antialias = false;
				renderer.setClearColor(0xFFFFFF, 0);
				renderer.shadowMap.enabled = true;
				renderer.shadowMapSoft = true;
				renderer.shadowMap.type = THREE.PCFSoftShadowMap;

				// ADDING THE CANVAS INTO THE CONTAINER
				document.getElementsByClassName("endlessrunner-container")[0].appendChild(renderer.domElement);

				// CREATING THE SCENE
				scene = new THREE.Scene();

				// ADDING THE CAMERA
				camera = new THREE.PerspectiveCamera(45, containerWidth / containerHeight, 1, 3000);
				camera.position.set(cameraPositionXStart, 10, PLANE_LENGTH / 2 + PLANE_LENGTH / 25 - cameraPositionZStart);
				camera.rotation.y = (cameraDegreesStart * Math.PI)/180;
				scene.add(camera);

				// ADDING THE STONE ROAD
				stoneLoader = new THREE.TextureLoader();
				stoneTexture = stoneLoader.load(stoneImageData, function (texture)
					{
					stoneTexture.wrapS = texture.wrapT = THREE.RepeatWrapping;
					stoneTexture.offset.set( 0, 0 );
					stoneTexture.repeat.set( 1, 20 );
					});
				stoneMaterial = new THREE.MeshLambertMaterial({ map: stoneTexture });
				stoneGeometry = new THREE.BoxGeometry(PLANE_WIDTH, PLANE_LENGTH + PLANE_LENGTH / 10, 1);
				stoneRoad = new THREE.Mesh(stoneGeometry, stoneMaterial);
				stoneRoad.rotation.x = 1.570;
				stoneRoad.receiveShadow = true;
				scene.add(stoneRoad);

				// ADDING THE GRASS FLOOR
				grassLoader = new THREE.TextureLoader();
				grassTexture = grassLoader.load(grassImageData, function (texture)
					{
					grassTexture.wrapS = texture.wrapT = THREE.RepeatWrapping;
					grassTexture.offset.set( 0, 0 );
					grassTexture.repeat.set( 1, 20 );
					});
				grassMaterial = new THREE.MeshPhongMaterial( { map: grassTexture } );
				grassGeometry = new THREE.BoxGeometry(PLANE_WIDTH, PLANE_LENGTH + PLANE_LENGTH / 10, 1);
				grassLeft = new THREE.Mesh(grassGeometry, grassMaterial);
				grassLeft.receiveShadow = true;
				grassLeft.rotation.x = 1.570;
				grassLeft.position.x = -PLANE_WIDTH;
				grassLeft.position.y = 1;
				grassRight = grassLeft.clone();
				grassRight.position.x = PLANE_WIDTH;
				scene.add(grassLeft, grassRight);

				// ADDING THE OCEAN
				oceanLoader = new THREE.TextureLoader();
				oceanTexture = oceanLoader.load(oceanImageData, function (texture)
					{
					oceanTexture.wrapS = texture.wrapT = THREE.RepeatWrapping;
					oceanTexture.offset.set( 0, 0 );
					});
				oceanMaterial = new THREE.MeshPhongMaterial( { map: oceanTexture } );
				oceanGeometry = new THREE.BoxGeometry(window.innerWidth, 1300);
				oceanFloor = new THREE.Mesh(oceanGeometry, oceanMaterial);
				oceanFloor.receiveShadow = true;
				oceanFloor.rotation.x = 1.570;
				oceanFloor.position.x = 0;
				oceanFloor.position.y = -1;
				scene.add(oceanFloor);

				// ADDING THE LIGHTS
				var spotLight;
				var target;
				var targetGeometry;
				var targetMaterial;
				for (var i = 0; i < 5; i += 1)
					{
					targetGeometry = new THREE.BoxGeometry(1, 1, 1);
					targetMaterial = new THREE.MeshNormalMaterial();
					target = new THREE.Mesh(targetGeometry, targetMaterial);
					target.position.set(0, 2, i * PLANE_LENGTH / 5 - PLANE_LENGTH / 2.50);
					target.visible = false;
					scene.add(target);

					spotLight = new THREE.SpotLight(0xFFFFFF, 0.1);
					spotLight.position.set(150, i * PLANE_LENGTH / 5 - PLANE_LENGTH / 2.5, -350);
					spotLight.castShadow = true;
					spotLight.target = target;
					spotLight.shadow.mapSize.width = 1024;
					spotLight.shadow.mapSize.height = 1024;

					stoneRoad.add(spotLight);
					}
				directionalLight = new THREE.DirectionalLight(0xffffff, 1);
				directionalLight.position.set(0, 99, 0);
				hemisphereLight = new THREE.HemisphereLight(0xFFB74D, 0x37474F, 1);
				hemisphereLight.position.y = 500;
				scene.add(directionalLight,hemisphereLight);

				// ADDING THE SKY
				var canvas = document.createElement("canvas");
				canvas.width = 32;
				canvas.height = window.innerHeight;
				var gradient = canvas.getContext("2d").createLinearGradient(0,0,0,canvas.height);
				gradient.addColorStop(0,"#1e4877");
				gradient.addColorStop(0.5,"#4584b4");
				canvas.getContext("2d").fillStyle = gradient;
				canvas.getContext("2d").fillRect(0, 0, canvas.width, canvas.height);
				document.getElementsByClassName("endlessrunner-container")[0].style.background = "url(" + canvas.toDataURL("image/png") + ")";
				document.getElementsByClassName("endlessrunner-container")[0].style.backgroundSize = "32px 100%";

				// LOADING THE CLOUD GEOMETRY
				cloudGeometry = new THREE.STLLoader().parse(cloudModel);
				cloudGeometry.computeFaceNormals();
				cloudGeometry.computeVertexNormals();
				cloudGeometry.center();

				// LOADING THE CLOUD MATERIAL
				cloudMaterial = new THREE.MeshLambertMaterial({ color: 0xF8F8F8, flatShading: THREE.FlatShading, opacity: 0.7 });

				// ADDING THE FIRST CLOUD
				cloud1 = new THREE.Mesh(cloudGeometry, cloudMaterial);
				cloud1.position.x = cloudsLandscapeX1;
				cloud1.position.y = cloudsLandscapeY1;
				scene.add(cloud1);

				// ADDING THE SECOND CLOUD
				cloud2 = new THREE.Mesh(cloudGeometry, cloudMaterial);
				cloud2.position.x = cloudsLandscapeX2;
				cloud2.position.y = cloudsLandscapeY2;
				scene.add(cloud2);

				// ADDING THE THIRTH CLOUD
				cloud3 = new THREE.Mesh(cloudGeometry, cloudMaterial);
				cloud3.position.x = cloudsLandscapeX2 * -1;
				cloud3.position.y = cloudsLandscapeY2;
				scene.add(cloud3);

				// ADDING THE FOURTH CLOUD
				cloud4 = new THREE.Mesh(cloudGeometry, cloudMaterial);
				cloud4.position.x = cloudsLandscapeX1 * -1;
				cloud4.position.y = cloudsLandscapeY1;
				scene.add(cloud4);

				// UPDATING THE CLOUDS POSITIONS
				updateCloudsPositions();

				// ADDING THE BACKGROUND ENEMY
				backgroundEnemy = new THREE.ColladaLoader().parse(robotnikModel);
				backgroundEnemy.scene.traverse(function(node)
					{
					// CHECKING IF THE NODE HAS A MATERIAL
					if (node.material)
						{
						// CHECKING IF THAT MATERIAL IS AN ARRAY
						if (node.material[0])
								{
								// HIDING THE WIREFRAME
								if (node.material[0].name=="edge_color1989494255")
									{
									node.material[0].visible = false;
									}
								// PULLING DOWN THE PANEL THAT THE MODEL HAS (AN HORIZONTAL LINE CAN BE SEEN OTHERWISE)
								else if (node.material[0].name=="__LightGray_")
									{
									node.position.y = node.position.y - 100;
									}
								}
							// HIDING THE WIREFRAME
							else if(node.material.name=="edge_color1989494255")
								{
								node.material.visible = false;
								}
							// HIDING THE WIREFRAME
							else if (node.material.name=="edge_color000255")
								{
								node.material.visible = false
								}
							// FIXING THE GLASS
							else if(node.material.name=="Translucent_Glass_Blue")
								{
								node.material.depthTest = false;
								}
							}
						});
				backgroundEnemy.scene.position.z = -650;
				backgroundEnemy.scene.position.y = 10.5;
				backgroundEnemy.scene.position.x = -470;
				backgroundEnemy.scene.rotation.x = (-60 * Math.PI)/180;
				backgroundEnemy.scene.scale.x = 2.5;
				backgroundEnemy.scene.scale.y = 2.5;
				backgroundEnemy.scene.scale.z = 2.5;
				scene.add(backgroundEnemy.scene);

				// ADDING THE BLACK HOLE FROM WHERE ALL THE ELEMENTS WILL BE COMING FROM
				backgroundEnemyHoleGeometry = new THREE.CircleGeometry(29, 32);
				backgroundEnemyHoleMaterial = new THREE.MeshBasicMaterial({ color:0x000000 });
				backgroundEnemyHole = new THREE.Mesh(backgroundEnemyHoleGeometry, backgroundEnemyHoleMaterial);
				backgroundEnemyHole.position.x = -1.75;
				backgroundEnemyHole.position.y = 12;
				backgroundEnemyHole.position.z = -500;
				scene.add(backgroundEnemyHole);

				// LOADING THE RING GEOMETRY
				ringGeometry = new THREE.STLLoader().parse(ringModel);
				ringGeometry.computeFaceNormals();
				ringGeometry.computeVertexNormals();
				ringGeometry.center();

				// LOADING THE RING MATERIAL
				ringMaterial = new THREE.MeshLambertMaterial({ color: 0xD4AF37, flatShading: THREE.FlatShading });

				// LOADING THE ENEMY GEOMETRY
				enemyGeometry = new THREE.BoxGeometry(2.5, 2.5, 2.5, 2.5);

				// LOADING THE ENEMY MATERIAL
				enemyMaterial = new THREE.MeshLambertMaterial({ color: 0x29B6F6, flatShading: THREE.FlatShading });

				// ADDING A HIDDEN RING AND ENEMY MODELS IN ORDER TO PREVENT THE GAME TO FREEZE FOR A SECOND
				// DURING THE COMPILING OF UNLOADED MODELS
				var unusedObject1 = new THREE.Mesh(ringGeometry, ringMaterial);
				var unusedObject2 = createEnemy();
				unusedObject1.scale.set(0.1,0.1,0.1);
				unusedObject2.scale.set(0.1,0.1,0.1);
				unusedObject1.position.x = 0;
				unusedObject1.position.y = -10;
				unusedObject2.position.x = -6;
				unusedObject2.position.y = -10;
				scene.add(unusedObject1);
				scene.add(unusedObject2);

				// LOADING THE HERO
				heroLoader = new THREE.GLTFLoader().parse(sonicModel,null,function(gltf)
					{
					// SETTING THAT THE HERO MODEL WILL BE CASTING A SHADOW
					gltf.scene.traverse(function(node)
						{
						if (node instanceof THREE.Mesh)
							{
							node.castShadow = true;
							}
						});

					// GETTING THE HERO MODEL
					hero = gltf.scene;

					// SETTING THE HERO SCALE
					hero.scale.set(11,11,11);

					// SETTING THE HERO POSITION
					hero.position.x = 0;
					hero.position.y = 1;
					hero.position.z = 490;

					// SETTING THE HERO ROTATION
					hero.rotation.y = 66;

					// SETTING THE HERO ANIMATION
					heroActions = new THREE.AnimationMixer(hero);
					heroActions.clipAction(gltf.animations[0]).play();
					//heroActions.clipAction(gltf.animations[0]).stop();
					//console.log(gltf.animations); //shows all animations available

					// ADDING THE HERO TO THE SCENE
					scene.add(hero);
					});
				}

			function moveLeft()
				{
				// CHECKING IF THE INTRO IS DONE
				if (cameraIntroDone==true)
					{
					// CHECKING IF THE HERO CAN MOVE TO THE LEFT, IF THERE ISN'T A MOVING DESTINY AND IF THE HERO ISN'T MOVING TO THE RIGHT
					if (hero.position.x !== -(PLANE_WIDTH - PADDING) / 2 && movingDestinyX==null)
						{
						// SETTING THAT THE HERO WILL BE MOVING TO THE LEFT
						movingLeft = true;

						// SETTING THAT THE HERO MOVING DESTINY
						movingDestinyX = parseFloat(hero.position.x - (PLANE_WIDTH - PADDING) / 2).toFixed(0);
						}
					}
				}

			function moveRight()
				{
				// CHECKING IF THE INTRO IS DONE
				if (cameraIntroDone==true)
					{
					// CHECKING IF THE HERO CAN MOVE TO THE RIGHT, IF THERE ISN'T A MOVING DESTINY AND IF THE HERO ISN'T MOVING TO THE LEFT
					if (hero.position.x !== (PLANE_WIDTH - PADDING) / 2 && movingDestinyX==null)
						{
						// SETTING THAT THE HERO WILL BE MOVING TO THE RIGHT
						movingRight = true;

						// SETTING THAT THE HERO MOVING DESTINY
						movingDestinyX = parseFloat(hero.position.x + (PLANE_WIDTH - PADDING) / 2).toFixed(0);
						}
					}
				}

			function startGame()
				{
				// HIDING THE GAME OVER CONTAINER
				document.getElementsByClassName("endlessrunner-gameover-background")[0].style.display = "none";

				// SHOWING THE SOUND HANDLER
				document.getElementsByClassName("endlessrunner-soundhandler-container")[0].style.display = "block";

				// SHOWING THE SCORE LABEL
				document.getElementsByClassName("endlessrunner-score")[0].style.display = "block";

				// REMOVING THE FADE IN EFFECT FOR THE GAME OVER, SCORE, HIGHSCORE AND RESTART BUTTON
				document.getElementById("endlessrunner-gameover-label").className = "";
				document.getElementById("endlessrunner-gameover-score").className = "endlessrunner-gameover-score";
				document.getElementById("endlessrunner-gameover-score-value").className = "endlessrunner-gameover-score-value";
				document.getElementById("endlessrunner-gameover-high").className = "endlessrunner-gameover-high";
				document.getElementById("endlessrunner-gameover-high-value").className = "endlessrunner-gameover-high-value";
				document.getElementById("endlessrunner-gameover-facebook-container").className = "";
				document.getElementById("endlessrunner-gameover-restart-container").className = "";
				document.getElementById("endlessrunner-gameover-twitter-container").className = "";

				// SETTING THE INITIAL OBSTACLES COUNT
				OBSTACLES_COUNT = 5;

				// REMOVING ALL THE OBSTACLES FROM THE SCENE
				obstacles.forEach(function (element, index)
					{
					scene.remove(obstacles[index]);
					});

				// CLEARING THE OBSTACLES ARRAY
				obstacles = [];

				// CENTERING THE HERO
				hero.position.x = 0;

				// CLEARING THE SCORE
				score = 0;

				// UPDATING THE SCORE LABEL
				document.getElementsByClassName("endlessrunner-score-wrapper-value")[0].innerHTML = score;

				// CLEARING ALL MOVEMENTS
				movingLeft = false;
				movingRight = false;
				movingDestinyX = null;

				// PAUSING THE GAME OVER MUSIC
				pauseGameOver();

				// PLAYING THE GAME MUSIC
				playMusic();

				// CHECKING IF THE INTRO IS DONE
				if (cameraIntroDone==true)
					{
					// ADDING AN OBSTACLE
					addObstacle();
					}

				// SETTING THAT NEW OBSTACLES WILL BE ADDED EVERY 4 SECONDS
				obstaclesSpawnIntervalID = window.setInterval(function ()
					{
					// CHECKING IF THE GAME IS RUNNING AND THE INTRO IS DONE
					if (game_running==true && cameraIntroDone==true)
						{
						// CHECKING IF A NEW OBSTACLE CAN BE ADDED
						if (obstacles.length < OBSTACLES_COUNT)
							{
							addObstacle();
							}
						}
					}, 4000);

				// SETTING THAT OBSTACLES COUNT WILL BE UPDATED EVERY 30 SECONDS
				obstaclesCounterIntervalID = window.setInterval(function ()
					{
					// CHECKING IF THE GAME IS RUNNING AND THE INTRO IS DONE
					if (game_running==true && cameraIntroDone==true)
						{
						// UPDATING THE OBSTACLES COUNT
						OBSTACLES_COUNT += 1;
						}
					}, 10000);

				// RENDERING THE GAME
				render();

				// RESIZING THE CANVAS TO FIT THE WINDOW
				resizeCanvasToFitWindow();
				}

			function addObstacle()
				{
				try
					{
					// CREATING AN OBJECT
					var object;

					// CHECKING IF THE NUMBER IS ODD
					if (obstacles.length%2==1)
						{
						// CREATING THE RING MODEL
						object = new THREE.Mesh(ringGeometry, ringMaterial);

						// SCALING THE RING MODEL
						object.scale.x = 0.05;
						object.scale.y = 0.05;
						object.scale.z = 0.05;

						// POSITIONING THE RING MODEL
						object.position.set(getRandomPositionX(), 4, OBSTACLES_POSITION_Z);

						// SETTING THE RING MODEL NAME
						object.name = "ring";
						}
						else
						{
						// CREATING THE ENEMY MODEL
						object = createEnemy();

						// POSITIONING THE ENEMY MODEL
						object.position.set(getRandomPositionX(), 3.5, OBSTACLES_POSITION_Z);

						// SETTING THE ENEMY MODEL NAME
						object.name = "enemy";
						}

					// SETTING THAT THE MODEL WILL BE CASTING A SHADOW
					object.castShadow = true;

					// ADDING THE NEW OBSTACLE TO THE OBSTACLES ARRAY
					obstacles.push(object);

					// ADDING THE NEW OBSTACLE TO THE SCENE
					scene.add(object);
					}
					catch(err)
					{
					}
				}

			function createEnemy()
				{
				// SETTING THE MATERIALS
				var material = [
								new THREE.MeshLambertMaterial({ color: 0x8b0000, flatShading: THREE.FlatShading }), // DARKRED
								new THREE.MeshLambertMaterial({ color: 0xffffff, flatShading: THREE.FlatShading }), // GOLD
								];

				// CREATING THE SPHERE
				var c0 = new THREE.Mesh(new THREE.SphereGeometry(5, 12, 12));
				c0.position.y = 6;

				// CREATING THE FIRST SPIKE
				var c1 = new THREE.Mesh(new THREE.ConeGeometry(2, 5, 12));
				c1.position.x = -6;
				c1.position.y = 6;
				c1.position.z = 0;
				c1.rotation.z = (90 * Math.PI)/180;

				// CREATING THE SECOND SPIKE
				var c2 = new THREE.Mesh(new THREE.ConeGeometry(2, 5, 12));
				c2.position.x = 6;
				c2.position.y = 6;
				c2.position.z = 0;
				c2.rotation.z = (-90 * Math.PI)/180;

				// CREATING THE THIRD SPIKE
				var c3 = new THREE.Mesh(new THREE.ConeGeometry(2, 5, 12));
				c3.position.x = 0;
				c3.position.y = 6;
				c3.position.z = 6;
				c3.rotation.x = (90 * Math.PI)/180;

				// CREATING THE FOURTH SPIKE
				var c4 = new THREE.Mesh(new THREE.ConeGeometry(2, 5, 12));
				c4.position.x = 0;
				c4.position.y = 6;
				c4.position.z = -6;
				c4.rotation.x = (-90 * Math.PI)/180;

				// CREATING THE ENEMY GEOMETRY
				var g = new THREE.Geometry();
				c0.updateMatrix();
				c1.updateMatrix();
				c2.updateMatrix();
				c3.updateMatrix();
				c4.updateMatrix();
				g.merge(c0.geometry, c0.matrix);
				g.merge(c1.geometry, c1.matrix);
				g.merge(c2.geometry, c2.matrix);
				g.merge(c3.geometry, c3.matrix);
				g.merge(c4.geometry, c4.matrix);

				// SETTING THE MATERIALS FOR EACH GEOMETRY
				for (var i = 0; i < g.faces.length; i++)
					{
					// CHECKING IF THE FACE BELONGS TO THE FIRST OBJECT (THE SPHERE)
					if (i < c0.geometry.faces.length)
						{
						// SETTING THE FIRST COLOR INDEX (DARKRED)
						g.faces[i].materialIndex = 0;
						}
						else
						{
						// SETTING THE SECOND COLOR INDEX (GOLD)
						g.faces[i].materialIndex = 1;
						}
					}

				// CREATING THE ENEMY WITH A GEOMETRY AND MATERIAL
				var m = new THREE.Mesh(g, material);

				// SCALING THE ENEMY
				m.scale.x = 0.3;
				m.scale.y = 0.3;
				m.scale.z = 0.3;

				// RETURNING THE ENEMY
				return m;
				}

			function gameOver()
				{
				try
					{
					// STOPPING ALL THE ANIMATION PROCESSES
					cancelAnimationFrame(globalRenderID);
					window.clearInterval(obstaclesSpawnIntervalID);
					window.clearInterval(obstaclesCounterIntervalID);
					}
				catch(err)
					{
					}

				// PAUSING THE MUSIC
				pauseMusic();

				// PLAYING THE GAME OVER MUSIC
				playGameOver();

				// HIDING THE SOUND HANDLER
				document.getElementsByClassName("endlessrunner-soundhandler-container")[0].style.display = "none";

				// HIDING THE SCORE LABEL
				document.getElementsByClassName("endlessrunner-score")[0].style.display = "none";

				// UPDATING THE GAME OVER SCORE LABEL
				document.getElementsByClassName("endlessrunner-gameover-score-value")[0].innerHTML = (score * 50);

				// CHECKING IF THE USER SET A HIGH SCORE
				if ((score * 50)>getHighscore())
					{
					// UPDATING THE HIGHSCORE
					setHighscore((score * 50));
					}

				// UPDATING THE HIGH SCORE LABEL
				document.getElementsByClassName("endlessrunner-gameover-high-value")[0].innerHTML = getHighscore();

				// SHOWING THE GAME OVER CONTAINER
				document.getElementsByClassName("endlessrunner-gameover-background")[0].style.display = "block";

				// IMPLEMENTING THE FADE IN EFFECT FOR THE GAME OVER, SCORE, HIGHSCORE AND RESTART BUTTON
				document.getElementById("endlessrunner-gameover-label").className = "endlessrunner-gameover-fadein";
				document.getElementById("endlessrunner-gameover-score").className = "endlessrunner-gameover-score endlessrunner-gameover-fadein";
				document.getElementById("endlessrunner-gameover-score-value").className = "endlessrunner-gameover-score-value endlessrunner-gameover-fadein";
				document.getElementById("endlessrunner-gameover-high").className = "endlessrunner-gameover-high endlessrunner-gameover-fadein";
				document.getElementById("endlessrunner-gameover-high-value").className = "endlessrunner-gameover-high-value endlessrunner-gameover-fadein";
				document.getElementById("endlessrunner-gameover-facebook-container").className = "endlessrunner-gameover-fadein";
				document.getElementById("endlessrunner-gameover-restart-container").className = "endlessrunner-gameover-fadein";
				document.getElementById("endlessrunner-gameover-twitter-container").className = "endlessrunner-gameover-fadein";
				}

			function playRing()
				{
				try
					{
					// CHECKING IF THE SOUND IS ENABLED
					if (soundEnabled==true)
						{
						// PLAYING THE RING SOUND
						audioContextSoundPlayer = audioContextSound.createBufferSource();
						audioContextSoundPlayer.buffer = audioContextRingSoundBuffer;
						audioContextSoundPlayer.connect(audioContextSound.destination);
						audioContextSoundPlayer.start(0);
						}
					}
					catch(err)
					{
					}
				}

			function playMusic()
				{
				try
					{
					// CHECKING IF THE SOUND IS ENABLED
					if (soundEnabled==true)
						{
						// PAUSING THE MUSIC (IF PLAYING)
						try{audioContextMusicPlayer.stop();}catch(err){}

						// CHECKING IF THE MUSIC VOLUME WAS CONFIGURED
						if (audioContextMusicVolume==null)
							{
							// SETTING THE MUSIC VOLUME TO 70%
							audioContextMusicVolume = audioContextMusic.createGain();
							audioContextMusicVolume.gain.value = 0.7;
							audioContextMusicVolume.connect(audioContextMusic.destination);
							}

						// PLAYING THE GAME MUSIC
						audioContextMusicPlayer = audioContextMusic.createBufferSource();
						audioContextMusicPlayer.buffer = audioContextGameMusicBuffer;
						audioContextMusicPlayer.connect(audioContextMusicVolume);
						audioContextMusicPlayer.loop = true;
						audioContextMusicPlayer.start(0);
						}
					}
					catch(err)
					{
					}
				}

			function resumeMusic()
				{
				try
					{
					// CHECKING IF THE SOUND IS ENABLED
					if (soundEnabled==true)
						{
						// CHECKING IF THE MUSIC VOLUME WAS CONFIGURED
						if (audioContextMusicVolume==null)
							{
							// SETTING THE MUSIC VOLUME TO 70%
							audioContextMusicVolume = audioContextMusic.createGain();
							audioContextMusicVolume.gain.value = 0.7;
							audioContextMusicVolume.connect(audioContextMusic.destination);
							}

						// RESUMING THE GAME MUSIC
						audioContextMusicPlayer = audioContextMusic.createBufferSource();
						audioContextMusicPlayer.buffer = audioContextGameMusicBuffer;
						audioContextMusicPlayer.connect(audioContextMusicVolume);
						audioContextMusicPlayer.loop = true;
						audioContextMusicPlayer.start(0);
						}
					}
					catch(err)
					{
					}
				}

			function pauseMusic()
				{
				try
					{
					// PAUSING THE GAME MUSIC
					audioContextMusicPlayer.stop();
					}
					catch(err)
					{
					}
				}

			function playGameOver()
				{
				try
					{
					// CHECKING IF THE SOUND IS ENABLED
					if (soundEnabled==true)
						{
						// PAUSING THE MUSIC (IF PLAYING)
						try{audioContextMusicPlayer.stop();}catch(err){}

						// CHECKING IF THE MUSIC VOLUME WAS CONFIGURED
						if (audioContextMusicVolume==null)
							{
							// SETTING THE MUSIC VOLUME TO 70%
							audioContextMusicVolume = audioContextMusic.createGain();
							audioContextMusicVolume.gain.value = 0.7;
							audioContextMusicVolume.connect(audioContextMusic.destination);
							}

						// PLAYING THE GAME OVER MUSIC
						audioContextMusicPlayer = audioContextMusic.createBufferSource();
						audioContextMusicPlayer.buffer = audioContextGameOverBuffer;
						audioContextMusicPlayer.connect(audioContextMusicVolume);
						audioContextMusicPlayer.loop = false;
						audioContextMusicPlayer.start(0);
						}
					}
					catch(err)
					{
					}
				}

			function resumeGameOver()
				{
				try
					{
					// CHECKING IF THE SOUND IS ENABLED
					if (soundEnabled==true)
						{
						// CHECKING IF THE MUSIC VOLUME WAS CONFIGURED
						if (audioContextMusicVolume==null)
							{
							// SETTING THE MUSIC VOLUME TO 70%
							audioContextMusicVolume = audioContextMusic.createGain();
							audioContextMusicVolume.gain.value = 0.7;
							audioContextMusicVolume.connect(audioContextMusic.destination);
							}

						// PLAYING THE GAME OVER MUSIC
						audioContextMusicPlayer = audioContextMusic.createBufferSource();
						audioContextMusicPlayer.buffer = audioContextGameOverBuffer;
						audioContextMusicPlayer.connect(audioContextMusicVolume);
						audioContextMusicPlayer.loop = false;
						audioContextMusicPlayer.start(0);
						}
					}
					catch(err)
					{
					}
				}

			function pauseGameOver()
				{
				try
					{
					// PAUSING THE GAME OVER MUSIC
					audioContextMusicPlayer.stop();
					}
					catch(err)
					{
					}
				}

			function soundToggle()
				{
				try
					{
					// CHECKING IF SOUND IS ENABLED
					if (soundEnabled==true)
						{
						// UPDATING THE VARIABLE
						soundEnabled = false;

						// PAUSING THE MUSIC
						pauseMusic();

						// SHOWING THE "SOUND OFF" ICON
						document.getElementById("endlessrunner-soundhandler").className = "endlessrunner-soundhandler-off";
						}
						else
						{
						// UPDATING THE VARIABLE
						soundEnabled = true;

						// PLAYING THE MUSIC 100 MS AFTER THE AUDIO CONTEXT WAS RESUMED
						setTimeout(function(){playMusic();},100);

						// SHOWING THE "SOUND ON" ICON
						document.getElementById("endlessrunner-soundhandler").className = "endlessrunner-soundhandler-on";
						}
					}
					catch(err)
					{
					}
				}

			function updateCloudsPositions()
				{
				try
					{
					// CHECKING THE WINDOW SIZE (DEVICE ORIENTATION)
					if (window.innerWidth>window.innerHeight)
						{
						// IN LANDSCAPE ORIENTATION THE CLOUDS ARE VISIBLE
						cloud1.visible = true;
						cloud2.visible = true;
						cloud3.visible = true;
						cloud4.visible = true;
						}
						else
						{
						// IN PORTRAIT ORIENTATION THE CLOUDS ARE HIDDEN
						cloud1.visible = false;
						cloud2.visible = false;
						cloud3.visible = false;
						cloud4.visible = false;
						}
					}
					catch(err)
					{
					}
				}

			function resizeCanvasToFitWindow()
				{
				try
					{
					// UPDATING THE CAMERA ASPECT
					camera.aspect = window.innerWidth / (window.innerHeight);

					// UPDATING THE CAMERA PROJECTION
					camera.updateProjectionMatrix();

					// UPDATING THE RENDERING SIZE
					renderer.setSize(window.innerWidth, window.innerHeight);

					// UPDATING THE CLOUDS POSITIONS
					updateCloudsPositions();
					}
					catch(err)
					{
					}
				}

			// LOADING THE GAME RESOURCES
			loadGameResources();

			window.oncontextmenu = function()
				{
				// DISABLING THE CONTEXT MENU
				return false;
				}

			window.addEventListener("blur", function()
				{
				try
					{
					// PAUSING THE GAME
					game_running = false;

					// CHECKING IF THE SOUND IS ENABLED
					if (soundEnabled==true)
						{
						// PAUSING THE GAME MUSIC
						pauseMusic();

						// PAUSING THE GAME OVER MUSIC
						pauseGameOver();
						}
					}
					catch(err)
					{
					}
				});

			window.addEventListener("focus", function()
				{
				try
					{
					// RESUMING THE GAME
					game_running = true;

					// CHECKING IF THE SOUND IS ENABLED
					if (soundEnabled==true)
						{
						// CHECKING IF THE GAME IS NOT OVER
						if (document.getElementsByClassName("endlessrunner-gameover-background")[0].style.display!=="block")
							{
							// RESUMING THE GAME MUSIC
							resumeMusic();
							}
							else
							{
							// RESUMING THE GAME OVER MUSIC
							resumeGameOver();
							}
						}
					}
					catch(err)
					{
					}
				});

			window.addEventListener("resize", function()
				{
				// RESIZING THE CANVAS TO FIT THE WINDOW
				resizeCanvasToFitWindow();
				});

			window.addEventListener("load", function()
				{
				// CHECKING IF IT IS AN ANDROID DEVICE AND NOT AN IOS DEVICE (BECAUSE IN IOS THE TOUCHSTART EVENT DOES NOT WORK TO RESUME THE AUDIO CONTEXT)
				if (isMobileDevice()==true && usingiOS()==false)
					{
					// SETTING WHAT WILL HAPPEN WHEN THE USER TOUCHES THE SOUND BUTTON CONTAINER
					document.getElementsByClassName("endlessrunner-soundhandler-container")[0].addEventListener("touchstart",function(event){soundToggle();});

					// SETTING WHAT WILL HAPPEN WHEN THE USER TOUCHES THE FACEBOOK BUTTON CONTAINER
					document.getElementById("endlessrunner-gameover-facebook-container").addEventListener("touchstart",function(event){window.open("https://www.facebook.com/sharer/sharer.php?u=" + encodeURIComponent(URL_TO_SHARE));});

					// SETTING WHAT WILL HAPPEN WHEN THE USER TOUCHES THE RESTART BUTTON CONTAINER
					document.getElementById("endlessrunner-gameover-restart-container").addEventListener("touchstart",function(event){startGame();});

					// SETTING WHAT WILL HAPPEN WHEN THE USER TOUCHES THE TWITTER BUTTON CONTAINER
					document.getElementById("endlessrunner-gameover-twitter-container").addEventListener("touchstart",function(event){window.open("https://www.twitter.com/share?url=" + encodeURIComponent(URL_TO_SHARE));});
					}
					else
					{
					// SETTING WHAT WILL HAPPEN WHEN THE USER CLICKS THE SOUND BUTTON CONTAINER
					document.getElementsByClassName("endlessrunner-soundhandler-container")[0].addEventListener("click",function(event){soundToggle();});

					// SETTING WHAT WILL HAPPEN WHEN THE USER CLICKS THE FACEBOOK BUTTON CONTAINER
					document.getElementById("endlessrunner-gameover-facebook-container").addEventListener("click",function(event){window.open("https://www.facebook.com/sharer/sharer.php?u=" + encodeURIComponent(URL_TO_SHARE));});

					// SETTING WHAT WILL HAPPEN WHEN THE USER CLICKS THE RESTART BUTTON CONTAINER
					document.getElementById("endlessrunner-gameover-restart-container").addEventListener("click",function(event){startGame();});

					// SETTING WHAT WILL HAPPEN WHEN THE USER CLICKS THE TWITTER BUTTON CONTAINER
					document.getElementById("endlessrunner-gameover-twitter-container").addEventListener("click",function(event){window.open("https://www.twitter.com/share?url=" + encodeURIComponent(URL_TO_SHARE));});
					}

				// HANDLING THE KEY EVENTS
				window.addEventListener("keydown", function ()
					{
					if (event.keyCode === 37 || event.keyCode === 65) // LEFT KEY OR A
						{
						// MOVING THE HERO TO THE LEFT
						moveLeft();
						}
					else if (event.keyCode === 39 || event.keyCode === 68) // RIGHT KEY OR D
						{
						// MOVING THE HERO TO THE RIGHT
						moveRight();
						}
					else if (event.keyCode === 32)
						{
						// CHECKING IF THE GAME IS OVER
						if (document.getElementsByClassName("endlessrunner-gameover-background")[0].style.display === "block")
							{
							// RESTARTING THE GAME
							startGame();
							}
						}
					});

				// HANDLING THE SWIPE LEFT AND RIGHT IN MOBILE DEVICES
				document.getElementsByClassName("endlessrunner-container")[0].addEventListener("touchstart",function(e){try{touchstartX=e.changedTouches[0].screenX;}catch(err){}});
				document.getElementsByClassName("endlessrunner-container")[0].addEventListener("touchend",function(e){try{touchendX=e.changedTouches[0].screenX;if(touchendX<touchstartX){moveLeft();}else if(touchendX>touchstartX){moveRight();}}catch(err){}});

				// UPDATING THE LABEL VALUES
				document.getElementsByClassName("endlessrunner-gameover-high")[0].innerHTML = STRING_HIGH;
				document.getElementsByClassName("endlessrunner-gameover-score")[0].innerHTML = STRING_SCORE;
				// document.getElementById("about").innerHTML = STRING_ABOUT;

				// HIDING THE LOADING SPLASH
				document.getElementsByClassName("endlessrunner-pleasewait")[0].style.display = "none";

				// SHOWING THE GAME CONTAINER
				document.getElementsByClassName("endlessrunner-container")[0].style.display = "block";

				// SHOWING THE SCORE LABEL
				document.getElementsByClassName("endlessrunner-score")[0].style.display = "block";

				// SHOWING THE SOUND HANDLER CONTAINER
				document.getElementsByClassName("endlessrunner-soundhandler-container")[0].style.display = "block";

				// SHOWING THE ABOUT BOX
				document.getElementById("about").style.display = "block";

				// STARTING THE GAME
				startGame();

				// FADING OUT THE ABOUT BOX
				setTimeout(function()
					{
					document.getElementById("about").className = "endlessrunner-about-label endlessrunner-about-hidden";
					}, 3000);

				// HIDING THE ABOUT BOX
				setTimeout(function()
					{
					document.getElementById("about").style.display = "none";
					}, 4500);
				});

			if ("serviceWorker" in navigator)
				{
				navigator.serviceWorker.register("worker.js").then(function(registration)
					{
					// Registration successful
					//console.log("ServiceWorker registration successful with scope: " + registration.scope);
					}).catch(function(err)
					{
					// Registration failed
					//console.log("ServiceWorker registration failed: " + err);
					});
				}
		</script>
	</body>
</html>